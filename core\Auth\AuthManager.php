<?php

namespace Core\Auth;

use Core\Database\Database;
use Core\Session\SessionManager;

class AuthManager
{
    private Database $database;
    private SessionManager $session;
    private ?array $user = null;

    public function __construct(Database $database, SessionManager $session)
    {
        $this->database = $database;
        $this->session = $session;
        $this->loadUser();
    }

    private function loadUser(): void
    {
        $userId = $this->session->get('user_id');
        
        if ($userId) {
            $this->user = $this->findUserById($userId);
            
            // If user not found, clear session
            if (!$this->user) {
                $this->logout();
            }
        }
    }

    public function attempt(string $email, string $password, bool $remember = false): bool
    {
        $user = $this->findUserByEmail($email);
        
        if (!$user || !$this->verifyPassword($password, $user['password_hash'])) {
            $this->recordFailedAttempt($email);
            return false;
        }

        // Check if account is active
        if ($user['status'] !== 'active') {
            return false;
        }

        // Check if account is locked due to failed attempts
        if ($this->isAccountLocked($email)) {
            return false;
        }

        // Login successful
        $this->login($user, $remember);
        $this->clearFailedAttempts($email);
        
        return true;
    }

    public function login(array $user, bool $remember = false): void
    {
        $this->user = $user;
        
        // Store user ID in session
        $this->session->set('user_id', $user['id']);
        $this->session->set('user_role', $user['role'] ?? 'user');
        
        // Regenerate session ID for security
        $this->session->regenerate();
        
        // Update last login
        $this->updateLastLogin($user['id']);
        
        // Handle remember me
        if ($remember) {
            $this->setRememberToken($user['id']);
        }
    }

    public function logout(): void
    {
        if ($this->user) {
            $this->clearRememberToken($this->user['id']);
        }
        
        $this->user = null;
        $this->session->destroy();
    }

    public function check(): bool
    {
        return $this->user !== null;
    }

    public function guest(): bool
    {
        return $this->user === null;
    }

    public function user(): ?array
    {
        return $this->user;
    }

    public function id(): ?int
    {
        return $this->user['id'] ?? null;
    }

    public function isAdmin(): bool
    {
        return $this->check() && ($this->user['role'] ?? 'user') === 'admin';
    }

    public function register(array $userData): ?int
    {
        // Hash password
        $userData['password_hash'] = $this->hashPassword($userData['password']);
        unset($userData['password']);
        
        // Set default values
        $userData['status'] = 'active';
        $userData['role'] = 'user';
        $userData['email_verified_at'] = null;
        
        try {
            return $this->database->insert('users', $userData);
        } catch (\Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return null;
        }
    }

    public function updatePassword(int $userId, string $newPassword): bool
    {
        $hashedPassword = $this->hashPassword($newPassword);
        
        try {
            $affected = $this->database->update('users', 
                ['password_hash' => $hashedPassword], 
                ['id' => $userId]
            );
            return $affected > 0;
        } catch (\Exception $e) {
            error_log("Password update error: " . $e->getMessage());
            return false;
        }
    }

    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    public function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_ARGON2ID);
    }

    private function findUserById(int $id): ?array
    {
        $sql = "SELECT * FROM users WHERE id = :id AND status = 'active' LIMIT 1";
        return $this->database->fetch($sql, ['id' => $id]);
    }

    private function findUserByEmail(string $email): ?array
    {
        $sql = "SELECT * FROM users WHERE email = :email LIMIT 1";
        return $this->database->fetch($sql, ['email' => $email]);
    }

    private function updateLastLogin(int $userId): void
    {
        $sql = "UPDATE users SET last_login_at = NOW() WHERE id = :id";
        $this->database->query($sql, ['id' => $userId]);
    }

    private function recordFailedAttempt(string $email): void
    {
        $sql = "
            INSERT INTO login_attempts (email, attempted_at, ip_address) 
            VALUES (:email, NOW(), :ip)
        ";
        
        $this->database->query($sql, [
            'email' => $email,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0'
        ]);
    }

    private function clearFailedAttempts(string $email): void
    {
        $sql = "DELETE FROM login_attempts WHERE email = :email";
        $this->database->query($sql, ['email' => $email]);
    }

    private function isAccountLocked(string $email): bool
    {
        $sql = "
            SELECT COUNT(*) as attempts 
            FROM login_attempts 
            WHERE email = :email 
            AND attempted_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
        ";
        
        $result = $this->database->fetch($sql, ['email' => $email]);
        return (int) ($result['attempts'] ?? 0) >= 5;
    }

    private function setRememberToken(int $userId): void
    {
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60)); // 30 days
        
        // Store token in database
        $sql = "
            INSERT INTO remember_tokens (user_id, token, expires_at) 
            VALUES (:user_id, :token, :expires_at)
            ON DUPLICATE KEY UPDATE token = :token, expires_at = :expires_at
        ";
        
        $this->database->query($sql, [
            'user_id' => $userId,
            'token' => hash('sha256', $token),
            'expires_at' => $expires
        ]);
        
        // Set cookie
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
    }

    private function clearRememberToken(int $userId): void
    {
        $sql = "DELETE FROM remember_tokens WHERE user_id = :user_id";
        $this->database->query($sql, ['user_id' => $userId]);
        
        // Clear cookie
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }

    public function checkRememberToken(): bool
    {
        $token = $_COOKIE['remember_token'] ?? null;
        
        if (!$token) {
            return false;
        }
        
        $sql = "
            SELECT rt.user_id, u.* 
            FROM remember_tokens rt
            INNER JOIN users u ON rt.user_id = u.id
            WHERE rt.token = :token 
            AND rt.expires_at > NOW()
            AND u.status = 'active'
            LIMIT 1
        ";
        
        $result = $this->database->fetch($sql, ['token' => hash('sha256', $token)]);
        
        if ($result) {
            $this->login($result);
            return true;
        }
        
        return false;
    }
}
