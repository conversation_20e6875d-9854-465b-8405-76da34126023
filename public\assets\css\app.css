/*
|--------------------------------------------------------------------------
| EticSimple CSS Framework
|--------------------------------------------------------------------------
| Monolitik e-ticaret sistemi için temel CSS
| Tema sistemi ile uyumlu CSS Custom Properties
|--------------------------------------------------------------------------
*/

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Custom Properties - Default Light Theme */
:root {
    /* Colors */
    --theme-bg-primary: #ffffff;
    --theme-bg-secondary: #f9fafb;
    --theme-bg-tertiary: #f3f4f6;
    --theme-text-primary: #111827;
    --theme-text-secondary: #6b7280;
    --theme-text-muted: #9ca3af;
    --theme-border-primary: #e5e7eb;
    --theme-border-secondary: #d1d5db;
    --theme-accent-primary: #0ea5e9;
    --theme-accent-secondary: #d946ef;
    --theme-success: #22c55e;
    --theme-warning: #f59e0b;
    --theme-error: #ef4444;
    --theme-info: #3b82f6;

    /* Typography */
    --font-primary: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    --font-heading: 'Poppins', 'Inter', sans-serif;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
}

/* Dark Theme */
[data-theme="dark"] {
    --theme-bg-primary: #111827;
    --theme-bg-secondary: #1f2937;
    --theme-bg-tertiary: #374151;
    --theme-text-primary: #f9fafb;
    --theme-text-secondary: #d1d5db;
    --theme-text-muted: #9ca3af;
    --theme-border-primary: #374151;
    --theme-border-secondary: #4b5563;
}

/* Base Styles */
html {
    font-size: 16px;
    line-height: 1.6;
}

body {
    font-family: var(--font-primary);
    background-color: var(--theme-bg-primary);
    color: var(--theme-text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--space-4);
    color: var(--theme-text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--space-4);
    color: var(--theme-text-secondary);
}

a {
    color: var(--theme-accent-primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--theme-accent-secondary);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.section-title {
    text-align: center;
    margin-bottom: var(--space-8);
    font-size: 2rem;
    color: var(--theme-text-primary);
}

.section-footer {
    text-align: center;
    margin-top: var(--space-8);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 1rem;
    line-height: 1.5;
}

.btn-primary {
    background-color: var(--theme-accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--theme-accent-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);
    border: 1px solid var(--theme-border-primary);
}

.btn-secondary:hover {
    background-color: var(--theme-bg-tertiary);
}

.btn-outline {
    background-color: transparent;
    color: var(--theme-accent-primary);
    border: 2px solid var(--theme-accent-primary);
}

.btn-outline:hover {
    background-color: var(--theme-accent-primary);
    color: white;
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: 1.125rem;
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: 0.875rem;
}

/* Cards */
.card {
    background-color: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Forms */
.form-group {
    margin-bottom: var(--space-4);
}

.form-label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: 500;
    color: var(--theme-text-primary);
}

.form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--theme-border-primary);
    border-radius: var(--radius-md);
    background-color: var(--theme-bg-primary);
    color: var(--theme-text-primary);
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--theme-accent-primary);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-3);
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.25rem; }
    
    .hero-title {
        font-size: 2rem !important;
    }
    
    .categories-grid,
    .products-grid,
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
}
