<?php

namespace App\Repositories;

use Core\Database\Database;

abstract class BaseRepository
{
    protected Database $database;
    protected string $table;
    protected string $primaryKey = 'id';

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    public function findById(int $id): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
        return $this->database->fetch($sql, ['id' => $id]);
    }

    public function findBySlug(string $slug): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE slug = :slug LIMIT 1";
        return $this->database->fetch($sql, ['slug' => $slug]);
    }

    public function getAll(int $limit = null, int $offset = 0, array $conditions = []): array
    {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];

        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }

        $sql .= " ORDER BY {$this->primaryKey} DESC";

        if ($limit) {
            $sql .= " LIMIT :limit";
            $params['limit'] = $limit;
            
            if ($offset > 0) {
                $sql .= " OFFSET :offset";
                $params['offset'] = $offset;
            }
        }

        return $this->database->fetchAll($sql, $params);
    }

    public function count(array $conditions = []): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $params = [];

        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }

        $result = $this->database->fetch($sql, $params);
        return (int) ($result['count'] ?? 0);
    }

    public function create(array $data): int
    {
        // Add timestamps
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->database->insert($this->table, $data);
    }

    public function update(int $id, array $data): bool
    {
        // Add updated timestamp
        $data['updated_at'] = date('Y-m-d H:i:s');

        $affected = $this->database->update($this->table, $data, [$this->primaryKey => $id]);
        return $affected > 0;
    }

    public function delete(int $id): bool
    {
        $affected = $this->database->delete($this->table, [$this->primaryKey => $id]);
        return $affected > 0;
    }

    public function exists(int $id): bool
    {
        $sql = "SELECT 1 FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
        $result = $this->database->fetch($sql, ['id' => $id]);
        return $result !== null;
    }

    public function slugExists(string $slug, int $excludeId = null): bool
    {
        $sql = "SELECT 1 FROM {$this->table} WHERE slug = :slug";
        $params = ['slug' => $slug];

        if ($excludeId) {
            $sql .= " AND {$this->primaryKey} != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $sql .= " LIMIT 1";
        $result = $this->database->fetch($sql, $params);
        return $result !== null;
    }

    public function generateUniqueSlug(string $title, int $excludeId = null): string
    {
        $slug = $this->createSlug($title);
        $originalSlug = $slug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    protected function createSlug(string $text): string
    {
        // Türkçe karakterleri dönüştür
        $turkishChars = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
        $englishChars = ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'];
        $text = str_replace($turkishChars, $englishChars, $text);

        // Küçük harfe çevir
        $text = strtolower($text);

        // Özel karakterleri kaldır
        $text = preg_replace('/[^a-z0-9\s-]/', '', $text);

        // Boşlukları tire ile değiştir
        $text = preg_replace('/[\s-]+/', '-', $text);

        // Başındaki ve sonundaki tireleri kaldır
        $text = trim($text, '-');

        return $text;
    }

    protected function buildWhereClause(array $conditions): array
    {
        $whereClause = [];
        $params = [];

        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                // IN clause
                $placeholders = [];
                foreach ($value as $i => $v) {
                    $placeholder = "{$field}_{$i}";
                    $placeholders[] = ":{$placeholder}";
                    $params[$placeholder] = $v;
                }
                $whereClause[] = "{$field} IN (" . implode(', ', $placeholders) . ")";
            } elseif (strpos($field, ' ') !== false) {
                // Custom condition like "price >="
                [$column, $operator] = explode(' ', $field, 2);
                $placeholder = str_replace(' ', '_', $field);
                $whereClause[] = "{$column} {$operator} :{$placeholder}";
                $params[$placeholder] = $value;
            } else {
                // Simple equality
                $whereClause[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
        }

        return [
            'clause' => implode(' AND ', $whereClause),
            'params' => $params
        ];
    }

    public function beginTransaction(): bool
    {
        return $this->database->beginTransaction();
    }

    public function commit(): bool
    {
        return $this->database->commit();
    }

    public function rollback(): bool
    {
        return $this->database->rollback();
    }
}
