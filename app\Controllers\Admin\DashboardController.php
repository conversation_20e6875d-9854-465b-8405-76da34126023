<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;

class DashboardController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->requireAdmin();
    }

    public function index(): void
    {
        try {
            // Dashboard istatistikleri
            $stats = $this->getDashboardStats();
            
            // Son siparişler
            $recentOrders = $this->getRecentOrders();
            
            // Düşük stoklu ürünler
            $lowStockProducts = $this->getLowStockProducts();
            
            // Günlük satış grafiği verisi
            $salesData = $this->getSalesData();
            
            $data = [
                'title' => 'Admin Dashboard',
                'stats' => $stats,
                'recentOrders' => $recentOrders,
                'lowStockProducts' => $lowStockProducts,
                'salesData' => $salesData,
                'user' => $this->user(),
            ];

            $this->view('admin/pages/dashboard', $data);
            
        } catch (\Exception $e) {
            error_log("Admin dashboard error: " . $e->getMessage());
            
            $this->view('admin/pages/error', [
                'title' => 'Hata',
                'message' => 'Dashboard yüklenirken bir hata oluştu.',
                'user' => $this->user(),
            ]);
        }
    }

    private function getDashboardStats(): array
    {
        $database = $this->app->getDatabase();
        
        try {
            // Toplam siparişler
            $totalOrders = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'] ?? 0;
            
            // Bu ayki siparişler
            $monthlyOrders = $database->fetch("
                SELECT COUNT(*) as count 
                FROM orders 
                WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) 
                AND YEAR(created_at) = YEAR(CURRENT_DATE())
            ")['count'] ?? 0;
            
            // Toplam müşteriler
            $totalCustomers = $database->fetch("
                SELECT COUNT(*) as count 
                FROM users 
                WHERE role = 'user' AND status = 'active'
            ")['count'] ?? 0;
            
            // Toplam ürünler
            $totalProducts = $database->fetch("
                SELECT COUNT(*) as count 
                FROM products 
                WHERE status = 'active'
            ")['count'] ?? 0;
            
            // Bu ayki gelir
            $monthlyRevenue = $database->fetch("
                SELECT COALESCE(SUM(total_amount), 0) as revenue 
                FROM orders 
                WHERE payment_status = 'paid'
                AND MONTH(created_at) = MONTH(CURRENT_DATE()) 
                AND YEAR(created_at) = YEAR(CURRENT_DATE())
            ")['revenue'] ?? 0;
            
            // Bekleyen siparişler
            $pendingOrders = $database->fetch("
                SELECT COUNT(*) as count 
                FROM orders 
                WHERE status = 'pending'
            ")['count'] ?? 0;
            
            return [
                'total_orders' => (int) $totalOrders,
                'monthly_orders' => (int) $monthlyOrders,
                'total_customers' => (int) $totalCustomers,
                'total_products' => (int) $totalProducts,
                'monthly_revenue' => (float) $monthlyRevenue,
                'pending_orders' => (int) $pendingOrders,
            ];
            
        } catch (\Exception $e) {
            error_log("Dashboard stats error: " . $e->getMessage());
            return [
                'total_orders' => 0,
                'monthly_orders' => 0,
                'total_customers' => 0,
                'total_products' => 0,
                'monthly_revenue' => 0,
                'pending_orders' => 0,
            ];
        }
    }

    private function getRecentOrders(int $limit = 10): array
    {
        $database = $this->app->getDatabase();
        
        try {
            $sql = "
                SELECT o.*, u.first_name, u.last_name, u.email
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                ORDER BY o.created_at DESC
                LIMIT :limit
            ";
            
            return $database->fetchAll($sql, ['limit' => $limit]);
            
        } catch (\Exception $e) {
            error_log("Recent orders error: " . $e->getMessage());
            return [];
        }
    }

    private function getLowStockProducts(int $limit = 10): array
    {
        $database = $this->app->getDatabase();
        
        try {
            $sql = "
                SELECT p.id, p.name, p.sku, p.inventory_quantity, p.price
                FROM products p
                WHERE p.track_inventory = 1 
                AND p.inventory_quantity <= 5
                AND p.status = 'active'
                ORDER BY p.inventory_quantity ASC
                LIMIT :limit
            ";
            
            return $database->fetchAll($sql, ['limit' => $limit]);
            
        } catch (\Exception $e) {
            error_log("Low stock products error: " . $e->getMessage());
            return [];
        }
    }

    private function getSalesData(int $days = 30): array
    {
        $database = $this->app->getDatabase();
        
        try {
            $sql = "
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as orders,
                    COALESCE(SUM(total_amount), 0) as revenue
                FROM orders 
                WHERE payment_status = 'paid'
                AND created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL :days DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC
            ";
            
            $results = $database->fetchAll($sql, ['days' => $days]);
            
            // Format data for charts
            $chartData = [
                'labels' => [],
                'orders' => [],
                'revenue' => []
            ];
            
            foreach ($results as $row) {
                $chartData['labels'][] = date('d.m', strtotime($row['date']));
                $chartData['orders'][] = (int) $row['orders'];
                $chartData['revenue'][] = (float) $row['revenue'];
            }
            
            return $chartData;
            
        } catch (\Exception $e) {
            error_log("Sales data error: " . $e->getMessage());
            return [
                'labels' => [],
                'orders' => [],
                'revenue' => []
            ];
        }
    }
}
