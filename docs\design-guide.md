# EticSimple E-Ticaret Sistemi - Ta<PERSON><PERSON>m Rehberi

## 🎨 Genel Tasarım Felsefesi
EticSimple, monolitik MVC mimarisine dayalı, modern, kullanıcı dostu ve Türkiye pazarına özel tasarlanmış bir e-ticaret platformudur. Tasarım yaklaşımımız:
- **Minimalist ve temiz** görünüm
- **Mobile-first** responsive tasarım
- **Template-driven** UI (Blade benzeri template engine)
- **Accessibility** (erişilebilirlik) odaklı
- **Türk kullanıcı alışkanlıklarına** uygun
- **Global standartlara** uyumlu

---

## 🎯 Renk Paleti

### Ana Renk Paleti
```css
:root {
  /* Primary Colors */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;  /* Ana marka rengi */
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Secondary Colors */
  --secondary-50: #fdf4ff;
  --secondary-100: #fae8ff;
  --secondary-200: #f5d0fe;
  --secondary-300: #f0abfc;
  --secondary-400: #e879f9;
  --secondary-500: #d946ef;  /* İkincil renk */
  --secondary-600: #c026d3;
  --secondary-700: #a21caf;
  --secondary-800: #86198f;
  --secondary-900: #701a75;
}
```

### Nötr Renk Paleti
```css
:root {
  /* Gray Scale */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* White & Black */
  --white: #ffffff;
  --black: #000000;
}
```

### Durum Renkleri
```css
:root {
  /* Success */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  /* Warning */
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  /* Error */
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Info */
  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;
}
```

---

## 📝 Tipografi

### Font Aileleri
```css
:root {
  /* Ana font - Türkçe karakterler için optimize */
  --font-primary: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  
  /* Başlıklar için */
  --font-heading: 'Poppins', 'Inter', sans-serif;
  
  /* Kod ve monospace */
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  
  /* Türkçe özel karakterler için fallback */
  --font-turkish: 'Inter', 'Source Sans Pro', 'Noto Sans', sans-serif;
}
```

### Font Boyutları ve Ağırlıkları
```css
:root {
  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}
```

---

## 📐 Spacing ve Layout

### Spacing Sistemi
```css
:root {
  /* Spacing Scale (8px base) */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
}
```

### Container ve Grid
```css
:root {
  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* Grid Columns */
  --grid-cols-12: repeat(12, minmax(0, 1fr));
  --grid-cols-6: repeat(6, minmax(0, 1fr));
  --grid-cols-4: repeat(4, minmax(0, 1fr));
  --grid-cols-3: repeat(3, minmax(0, 1fr));
  --grid-cols-2: repeat(2, minmax(0, 1fr));
}
```

---

## 🎭 Bileşen Tasarım Sistemi

### Butonlar
```css
/* Primary Button */
.btn-primary {
  background: var(--primary-500);
  color: var(--white);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

/* Secondary Button */
.btn-secondary {
  background: transparent;
  color: var(--primary-500);
  border: 2px solid var(--primary-500);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
}
```

### Kartlar
```css
.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: var(--space-6);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}
```

### Form Elemanları
```css
.input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}
```

---

## 📱 Responsive Breakpoints

```css
:root {
  /* Mobile First Breakpoints */
  --bp-sm: 640px;   /* Küçük tablet */
  --bp-md: 768px;   /* Tablet */
  --bp-lg: 1024px;  /* Küçük desktop */
  --bp-xl: 1280px;  /* Desktop */
  --bp-2xl: 1536px; /* Büyük desktop */
}

/* Media Query Mixins */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

---

## 🎨 Çoklu Tema Sistemi

### Tema Mimarisi
EticSimple dinamik tema sistemi CSS Custom Properties ve JavaScript tabanlı çalışır:

```css
:root {
  /* Tema Değişkenleri - Varsayılan (Light) */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f9fafb;
  --theme-bg-tertiary: #f3f4f6;
  --theme-text-primary: #111827;
  --theme-text-secondary: #6b7280;
  --theme-text-muted: #9ca3af;
  --theme-border-primary: #e5e7eb;
  --theme-border-secondary: #d1d5db;
  --theme-accent-primary: var(--primary-500);
  --theme-accent-secondary: var(--secondary-500);
  --theme-success: var(--success-500);
  --theme-warning: var(--warning-500);
  --theme-error: var(--error-500);
  --theme-info: var(--info-500);
}
```

### Tema Varyantları

#### Light Theme (Varsayılan)
```css
[data-theme="light"] {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f9fafb;
  --theme-bg-tertiary: #f3f4f6;
  --theme-text-primary: #111827;
  --theme-text-secondary: #6b7280;
  --theme-text-muted: #9ca3af;
  --theme-border-primary: #e5e7eb;
  --theme-border-secondary: #d1d5db;
}
```

#### Dark Theme
```css
[data-theme="dark"] {
  --theme-bg-primary: #111827;
  --theme-bg-secondary: #1f2937;
  --theme-bg-tertiary: #374151;
  --theme-text-primary: #f9fafb;
  --theme-text-secondary: #d1d5db;
  --theme-text-muted: #9ca3af;
  --theme-border-primary: #374151;
  --theme-border-secondary: #4b5563;
}
```

#### Blue Theme (Kurumsal)
```css
[data-theme="blue"] {
  --theme-bg-primary: #f8fafc;
  --theme-bg-secondary: #f1f5f9;
  --theme-bg-tertiary: #e2e8f0;
  --theme-text-primary: #0f172a;
  --theme-text-secondary: #475569;
  --theme-text-muted: #64748b;
  --theme-border-primary: #cbd5e1;
  --theme-border-secondary: #94a3b8;
  --theme-accent-primary: #3b82f6;
  --theme-accent-secondary: #1e40af;
}
```

#### Green Theme (Doğa)
```css
[data-theme="green"] {
  --theme-bg-primary: #f7fdf7;
  --theme-bg-secondary: #f0fdf4;
  --theme-bg-tertiary: #dcfce7;
  --theme-text-primary: #14532d;
  --theme-text-secondary: #166534;
  --theme-text-muted: #22c55e;
  --theme-border-primary: #bbf7d0;
  --theme-border-secondary: #86efac;
  --theme-accent-primary: #22c55e;
  --theme-accent-secondary: #16a34a;
}
```

### Tema Değişim Sistemi

#### JavaScript Tema Controller
```javascript
class ThemeController {
  constructor() {
    this.currentTheme = this.getStoredTheme() || 'light';
    this.applyTheme(this.currentTheme);
  }

  applyTheme(themeName) {
    document.documentElement.setAttribute('data-theme', themeName);
    localStorage.setItem('selected-theme', themeName);
    this.currentTheme = themeName;
    this.notifyThemeChange(themeName);
  }

  getAvailableThemes() {
    return ['light', 'dark', 'blue', 'green'];
  }

  getStoredTheme() {
    return localStorage.getItem('selected-theme');
  }

  notifyThemeChange(themeName) {
    // Server'a tema değişikliğini bildir
    if (window.user && window.user.isLoggedIn) {
      fetch('/account/theme', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ theme: themeName })
      });
    }
  }
}
```

#### CSS Tema Uygulaması
```css
/* Tüm bileşenler tema değişkenlerini kullanır */
.card {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-primary);
  color: var(--theme-text-primary);
}

.btn-primary {
  background: var(--theme-accent-primary);
  color: var(--theme-bg-primary);
}

.text-primary {
  color: var(--theme-text-primary);
}

.text-secondary {
  color: var(--theme-text-secondary);
}

.border {
  border-color: var(--theme-border-primary);
}
```

### Tema Önizleme Sistemi

#### Önizleme Modal Yapısı
```html
<div class="theme-preview-modal">
  <div class="theme-preview-header">
    <h3>Tema Önizleme: {theme_name}</h3>
    <button class="close-preview">×</button>
  </div>

  <div class="theme-preview-content" data-theme="{preview_theme}">
    <!-- Örnek içerik -->
    <div class="preview-section">
      <div class="card">
        <h4>Örnek Kart</h4>
        <p>Bu tema ile kartların nasıl görüneceğini gösterir.</p>
        <button class="btn-primary">Birincil Buton</button>
        <button class="btn-secondary">İkincil Buton</button>
      </div>
    </div>
  </div>

  <div class="theme-preview-actions">
    <button class="btn-secondary cancel-preview">İptal</button>
    <button class="btn-primary apply-theme">Temayı Uygula</button>
  </div>
</div>
```

#### Tema Seçici Bileşeni
```css
.theme-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  padding: var(--space-6);
}

.theme-option {
  border: 2px solid var(--theme-border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.theme-option:hover {
  border-color: var(--theme-accent-primary);
  transform: translateY(-2px);
}

.theme-option.active {
  border-color: var(--theme-accent-primary);
  background: var(--theme-bg-secondary);
}

.theme-preview-colors {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.color-swatch {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid var(--theme-border-secondary);
}
```

### Admin Panel Tema Yönetimi

#### Tema Yönetim Arayüzü
```html
<div class="admin-theme-management">
  <div class="theme-list">
    <div class="theme-item" data-theme-id="{theme_id}">
      <div class="theme-info">
        <img src="{preview_image}" alt="{theme_name}" class="theme-thumbnail">
        <div class="theme-details">
          <h4>{theme_name}</h4>
          <p>{theme_description}</p>
          <span class="theme-version">v{version}</span>
          <span class="theme-author">by {author}</span>
        </div>
      </div>

      <div class="theme-actions">
        <button class="btn-sm btn-secondary preview-theme">Önizle</button>
        <button class="btn-sm btn-primary activate-theme">Aktifleştir</button>
        <button class="btn-sm btn-outline edit-theme">Düzenle</button>
        <button class="btn-sm btn-danger delete-theme">Sil</button>
      </div>

      <div class="theme-status">
        <span class="status-badge active" v-if="theme.is_active">Aktif</span>
        <span class="status-badge default" v-if="theme.is_default">Varsayılan</span>
      </div>
    </div>
  </div>

  <div class="theme-upload">
    <h3>Yeni Tema Yükle</h3>
    <form class="theme-upload-form">
      <input type="file" accept=".zip" name="theme_file">
      <button type="submit" class="btn-primary">Tema Yükle</button>
    </form>
  </div>
</div>
```

#### Tema Özelleştirme Paneli
```css
.theme-customization-panel {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--space-6);
  height: 100vh;
}

.customization-sidebar {
  background: var(--theme-bg-secondary);
  padding: var(--space-6);
  overflow-y: auto;
}

.customization-group {
  margin-bottom: var(--space-6);
}

.customization-group h4 {
  margin-bottom: var(--space-3);
  color: var(--theme-text-primary);
}

.color-picker-input {
  width: 100%;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
}

.customization-preview {
  background: var(--theme-bg-primary);
  padding: var(--space-6);
  overflow-y: auto;
}
```

---

## 🔧 Template Engine Sistemi

### Blade Benzeri Template Syntax
EticSimple, Laravel Blade'e benzer bir template engine kullanır:

#### Temel Syntax
```php
<!-- Değişken yazdırma -->
{{ $product->name }}
{{ $user->email ?? 'Misafir' }}

<!-- HTML escape edilmemiş içerik -->
{!! $product->description !!}

<!-- Koşullu ifadeler -->
@if($user->isLoggedIn())
    <p>Hoş geldin, {{ $user->name }}!</p>
@else
    <p>Lütfen giriş yapın.</p>
@endif

<!-- Döngüler -->
@foreach($products as $product)
    <div class="product-card">
        <h3>{{ $product->name }}</h3>
        <p>{{ $product->price }} ₺</p>
    </div>
@endforeach

<!-- Layout inheritance -->
@extends('layouts.app')

@section('title', 'Ürün Listesi')

@section('content')
    <h1>Ürünlerimiz</h1>
    <!-- İçerik buraya -->
@endsection
```

#### Component Sistemi
```php
<!-- Component kullanımı -->
@component('components.product-card', ['product' => $product])
@endcomponent

<!-- Slot ile component -->
@component('components.modal')
    @slot('title')
        Ürün Detayı
    @endslot

    @slot('content')
        {{ $product->description }}
    @endslot
@endcomponent
```

#### Include Sistemi
```php
<!-- Partial template include -->
@include('partials.header')
@include('partials.navigation', ['activeMenu' => 'products'])
@include('partials.footer')
```

### Layout Yapısı

#### Ana Layout (layouts/app.blade.php)
```php
<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" data-theme="{{ theme()->current() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title') - {{ config('app.name') }}</title>

    <!-- CSS -->
    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    @stack('styles')

    <!-- Theme CSS Variables -->
    <style>
        :root {
            @foreach(theme()->getVariables() as $key => $value)
                --{{ $key }}: {{ $value }};
            @endforeach
        }
    </style>
</head>
<body>
    @include('partials.header')

    <main class="main-content">
        @yield('content')
    </main>

    @include('partials.footer')

    <!-- JavaScript -->
    <script src="{{ asset('js/app.js') }}"></script>
    @stack('scripts')
</body>
</html>
```

#### Admin Layout (layouts/admin.blade.php)
```php
<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" data-theme="{{ theme()->current() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title') - Admin Panel</title>

    <link rel="stylesheet" href="{{ asset('css/admin.css') }}">
    @stack('styles')
</head>
<body class="admin-layout">
    @include('admin.partials.sidebar')

    <div class="admin-main">
        @include('admin.partials.header')

        <main class="admin-content">
            @yield('content')
        </main>
    </div>

    <script src="{{ asset('js/admin.js') }}"></script>
    @stack('scripts')
</body>
</html>
```

---

## 🌐 Türkiye Özel Tasarım Kılavuzları

### Para Birimi Gösterimi
- Türk Lirası sembolü: **₺** (Unicode: U+20BA)
- Format: `1.234,56 ₺` (Türkçe format)
- Binlik ayırıcı: nokta (.)
- Ondalık ayırıcı: virgül (,)

### Tarih ve Saat Formatları
- Tarih: `DD.MM.YYYY` (örn: 15.06.2025)
- Saat: `HH:MM` (24 saat formatı)
- Tam format: `DD.MM.YYYY HH:MM`

### Telefon Numarası Formatı
- Format: `+90 (5XX) XXX XX XX`
- Placeholder: `+90 (5XX) XXX XX XX`

---

## ♿ Erişilebilirlik (Accessibility)

### Renk Kontrastı
- Normal metin: minimum 4.5:1 kontrast oranı
- Büyük metin: minimum 3:1 kontrast oranı
- UI bileşenleri: minimum 3:1 kontrast oranı

### Klavye Navigasyonu
- Tüm interaktif elemanlar Tab ile erişilebilir
- Focus göstergeleri açık ve belirgin
- Skip links ana içeriğe geçiş için

### Screen Reader Desteği
- Semantic HTML kullanımı
- ARIA labels ve descriptions
- Alt text tüm görseller için

---

*Bu tasarım rehberi proje gelişimi sürecinde güncellenecektir.*
