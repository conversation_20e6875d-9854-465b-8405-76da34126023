<?php

namespace App\Models;

use Core\Application;

class User
{
    private array $attributes = [];
    private array $original = [];
    private bool $exists = false;

    public function __construct(array $attributes = [])
    {
        $this->fill($attributes);
        $this->original = $this->attributes;
        $this->exists = !empty($attributes['id']);
    }

    /**
     * Fill model with attributes
     */
    public function fill(array $attributes): self
    {
        foreach ($attributes as $key => $value) {
            $this->attributes[$key] = $value;
        }
        return $this;
    }

    /**
     * Get attribute value
     */
    public function __get(string $key)
    {
        return $this->attributes[$key] ?? null;
    }

    /**
     * Set attribute value
     */
    public function __set(string $key, $value): void
    {
        $this->attributes[$key] = $value;
    }

    /**
     * Check if attribute exists
     */
    public function __isset(string $key): bool
    {
        return isset($this->attributes[$key]);
    }

    /**
     * Get all attributes
     */
    public function toArray(): array
    {
        return $this->attributes;
    }

    /**
     * Get user's full name
     */
    public function getFullName(): string
    {
        return trim(($this->first_name ?? '') . ' ' . ($this->last_name ?? ''));
    }

    /**
     * Get user's initials
     */
    public function getInitials(): string
    {
        $firstName = $this->first_name ?? '';
        $lastName = $this->last_name ?? '';
        
        $initials = '';
        if (!empty($firstName)) {
            $initials .= strtoupper(substr($firstName, 0, 1));
        }
        if (!empty($lastName)) {
            $initials .= strtoupper(substr($lastName, 0, 1));
        }
        
        return $initials ?: 'U';
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return ($this->role ?? 'user') === 'admin';
    }

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return ($this->status ?? 'inactive') === 'active';
    }

    /**
     * Check if email is verified
     */
    public function isEmailVerified(): bool
    {
        return !empty($this->email_verified_at);
    }

    /**
     * Get user's theme
     */
    public function getTheme(): string
    {
        return $this->theme ?? 'light';
    }

    /**
     * Verify password
     */
    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->password ?? '');
    }

    /**
     * Update last login time
     */
    public function updateLastLogin(): bool
    {
        $this->last_login_at = date('Y-m-d H:i:s');
        return $this->save();
    }

    /**
     * Update last activity time
     */
    public function updateLastActivity(): bool
    {
        $this->last_activity_at = date('Y-m-d H:i:s');
        return $this->save();
    }

    /**
     * Check if user has permission
     */
    public function hasPermission(string $permission): bool
    {
        // Simple role-based permission system
        if ($this->isAdmin()) {
            return true; // Admin has all permissions
        }

        // Define user permissions
        $userPermissions = [
            'view_profile',
            'edit_profile',
            'view_orders',
            'create_orders',
            'view_addresses',
            'manage_addresses'
        ];

        return in_array($permission, $userPermissions);
    }

    /**
     * Get user's addresses
     */
    public function getAddresses(): array
    {
        if (!$this->id) {
            return [];
        }

        $app = Application::getInstance();
        $database = $app->getDatabase();

        $sql = "SELECT * FROM user_addresses WHERE user_id = :user_id ORDER BY is_default DESC, created_at DESC";
        return $database->fetchAll($sql, ['user_id' => $this->id]);
    }

    /**
     * Get user's default address
     */
    public function getDefaultAddress(): ?array
    {
        if (!$this->id) {
            return null;
        }

        $app = Application::getInstance();
        $database = $app->getDatabase();

        $sql = "SELECT * FROM user_addresses WHERE user_id = :user_id AND is_default = 1 LIMIT 1";
        return $database->fetch($sql, ['user_id' => $this->id]);
    }

    /**
     * Get user's orders
     */
    public function getOrders(int $limit = 10): array
    {
        if (!$this->id) {
            return [];
        }

        $app = Application::getInstance();
        $database = $app->getDatabase();

        $sql = "SELECT * FROM orders WHERE user_id = :user_id ORDER BY created_at DESC LIMIT :limit";
        return $database->fetchAll($sql, ['user_id' => $this->id, 'limit' => $limit]);
    }

    /**
     * Get user's order count
     */
    public function getOrderCount(): int
    {
        if (!$this->id) {
            return 0;
        }

        $app = Application::getInstance();
        $database = $app->getDatabase();

        $sql = "SELECT COUNT(*) as count FROM orders WHERE user_id = :user_id";
        $result = $database->fetch($sql, ['user_id' => $this->id]);
        
        return (int) ($result['count'] ?? 0);
    }

    /**
     * Save user to database
     */
    public function save(): bool
    {
        $app = Application::getInstance();
        $database = $app->getDatabase();

        try {
            if ($this->exists) {
                // Update existing user
                $changes = array_diff_assoc($this->attributes, $this->original);
                if (empty($changes)) {
                    return true; // No changes to save
                }

                $changes['updated_at'] = date('Y-m-d H:i:s');
                $affected = $database->update('users', $changes, ['id' => $this->id]);
                
                if ($affected > 0) {
                    $this->original = $this->attributes;
                    return true;
                }
            } else {
                // Create new user
                $this->attributes['created_at'] = date('Y-m-d H:i:s');
                $this->attributes['updated_at'] = date('Y-m-d H:i:s');
                
                $id = $database->insert('users', $this->attributes);
                
                if ($id) {
                    $this->attributes['id'] = $id;
                    $this->original = $this->attributes;
                    $this->exists = true;
                    return true;
                }
            }
        } catch (\Exception $e) {
            error_log("User save error: " . $e->getMessage());
        }

        return false;
    }

    /**
     * Delete user from database
     */
    public function delete(): bool
    {
        if (!$this->exists || !$this->id) {
            return false;
        }

        $app = Application::getInstance();
        $database = $app->getDatabase();

        try {
            $affected = $database->delete('users', ['id' => $this->id]);
            
            if ($affected > 0) {
                $this->exists = false;
                return true;
            }
        } catch (\Exception $e) {
            error_log("User delete error: " . $e->getMessage());
        }

        return false;
    }

    /**
     * Validate T.C. Kimlik No
     */
    public function validateTCKimlik(): bool
    {
        $tcKimlik = $this->tc_kimlik_no ?? '';
        
        if (empty($tcKimlik) || strlen($tcKimlik) !== 11) {
            return false;
        }

        $digits = str_split($tcKimlik);
        
        // İlk hane 0 olamaz
        if ($digits[0] === '0') {
            return false;
        }

        // Checksum kontrolü
        $sum1 = $digits[0] + $digits[2] + $digits[4] + $digits[6] + $digits[8];
        $sum2 = $digits[1] + $digits[3] + $digits[5] + $digits[7];
        
        $check1 = ($sum1 * 7 - $sum2) % 10;
        $check2 = ($sum1 + $sum2 + $digits[9]) % 10;
        
        return $check1 == $digits[9] && $check2 == $digits[10];
    }

    /**
     * Format phone number
     */
    public function getFormattedPhone(): string
    {
        $phone = $this->phone ?? '';
        
        if (empty($phone)) {
            return '';
        }

        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Format Turkish phone number
        if (strlen($phone) === 11 && substr($phone, 0, 1) === '0') {
            return '+90 ' . substr($phone, 1, 3) . ' ' . substr($phone, 4, 3) . ' ' . substr($phone, 7, 2) . ' ' . substr($phone, 9, 2);
        } elseif (strlen($phone) === 10) {
            return '+90 ' . substr($phone, 0, 3) . ' ' . substr($phone, 3, 3) . ' ' . substr($phone, 6, 2) . ' ' . substr($phone, 8, 2);
        }
        
        return $this->phone;
    }

    /**
     * Find user by ID
     */
    public static function find(int $id): ?self
    {
        $app = Application::getInstance();
        $database = $app->getDatabase();

        $sql = "SELECT * FROM users WHERE id = :id LIMIT 1";
        $userData = $database->fetch($sql, ['id' => $id]);

        return $userData ? new self($userData) : null;
    }

    /**
     * Find user by email
     */
    public static function findByEmail(string $email): ?self
    {
        $app = Application::getInstance();
        $database = $app->getDatabase();

        $sql = "SELECT * FROM users WHERE email = :email LIMIT 1";
        $userData = $database->fetch($sql, ['email' => $email]);

        return $userData ? new self($userData) : null;
    }
}
