<!DOCTYPE html>
<html lang="tr" data-theme="<?= $currentTheme['slug'] ?? 'light' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= $this->csrf() ?>">
    
    <title><?= isset($title) ? $this->e($title) . ' - ' : '' ?>EticSimple Admin</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= $this->asset('images/favicon.ico') ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?= $this->asset('css/app.css') ?>">
    <link rel="stylesheet" href="<?= $this->asset('css/admin.css') ?>">
    
    <!-- Theme CSS Variables -->
    <style>
        :root {
            <?php if (isset($currentTheme['css_variables'])): ?>
                <?php foreach ($currentTheme['css_variables'] as $key => $value): ?>
                    --<?= $key ?>: <?= $value ?>;
                <?php endforeach; ?>
            <?php endif; ?>
        }
    </style>
    
    <!-- Additional Styles -->
    <?php $this->yield('styles'); ?>
</head>
<body class="admin-layout">
    <!-- Admin Sidebar -->
    <?php $this->include('admin.components.sidebar', [
        'user' => $user ?? null
    ]); ?>
    
    <!-- Admin Main Content -->
    <div class="admin-main">
        <!-- Admin Header -->
        <?php $this->include('admin.components.header', [
            'user' => $user ?? null,
            'title' => $title ?? 'Dashboard'
        ]); ?>
        
        <!-- Admin Content -->
        <main class="admin-content">
            <!-- Flash Messages -->
            <?php $this->include('store.components.flash-messages'); ?>
            
            <!-- Page Content -->
            <?php $this->yield('content'); ?>
        </main>
        
        <!-- Admin Footer -->
        <?php $this->include('admin.components.footer'); ?>
    </div>
    
    <!-- JavaScript -->
    <script src="<?= $this->asset('js/app.js') ?>"></script>
    <script src="<?= $this->asset('js/admin.js') ?>"></script>
    
    <!-- Admin User Data -->
    <script>
        window.admin = {
            user: <?= json_encode($user ?? null) ?>,
            csrfToken: '<?= $this->csrf() ?>',
            baseUrl: '<?= $this->url() ?>'
        };
    </script>
    
    <!-- Additional Scripts -->
    <?php $this->yield('scripts'); ?>
</body>
</html>
