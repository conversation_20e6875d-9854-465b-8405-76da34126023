<?php

namespace App\Repositories;

class ThemeRepository extends BaseRepository
{
    protected string $table = 'themes';

    public function getActive(): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE is_active = 1 LIMIT 1";
        return $this->database->fetch($sql);
    }

    public function getDefault(): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE is_default = 1 LIMIT 1";
        return $this->database->fetch($sql);
    }

    public function activate(string $slug): bool
    {
        // First deactivate all themes
        $this->deactivateAll();
        
        // Then activate the selected theme
        $sql = "UPDATE {$this->table} SET is_active = 1, updated_at = NOW() WHERE slug = :slug";
        $statement = $this->database->query($sql, ['slug' => $slug]);
        
        return $statement->rowCount() > 0;
    }

    public function deactivateAll(): bool
    {
        $sql = "UPDATE {$this->table} SET is_active = 0, updated_at = NOW()";
        $statement = $this->database->query($sql);
        
        return $statement->rowCount() >= 0; // 0 or more rows affected is OK
    }

    public function setDefault(string $slug): bool
    {
        // First remove default from all themes
        $sql = "UPDATE {$this->table} SET is_default = 0, updated_at = NOW()";
        $this->database->query($sql);
        
        // Then set the selected theme as default
        $sql = "UPDATE {$this->table} SET is_default = 1, updated_at = NOW() WHERE slug = :slug";
        $statement = $this->database->query($sql, ['slug' => $slug]);
        
        return $statement->rowCount() > 0;
    }

    public function getAll(): array
    {
        $sql = "SELECT * FROM {$this->table} ORDER BY is_default DESC, is_active DESC, name ASC";
        return $this->database->fetchAll($sql);
    }

    public function getPublic(): array
    {
        $sql = "SELECT id, name, slug, description, preview_image, is_active, is_default FROM {$this->table} ORDER BY is_default DESC, name ASC";
        return $this->database->fetchAll($sql);
    }

    public function findBySlug(string $slug): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE slug = :slug LIMIT 1";
        return $this->database->fetch($sql, ['slug' => $slug]);
    }

    public function create(array $data): int
    {
        // Ensure required fields
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Convert css_variables to JSON if it's an array
        if (isset($data['css_variables']) && is_array($data['css_variables'])) {
            $data['css_variables'] = json_encode($data['css_variables']);
        }
        
        // Convert settings to JSON if it's an array
        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }

        return $this->database->insert($this->table, $data);
    }

    public function update(int $id, array $data): bool
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Convert css_variables to JSON if it's an array
        if (isset($data['css_variables']) && is_array($data['css_variables'])) {
            $data['css_variables'] = json_encode($data['css_variables']);
        }
        
        // Convert settings to JSON if it's an array
        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }

        $affected = $this->database->update($this->table, $data, ['id' => $id]);
        return $affected > 0;
    }

    public function updateCssVariables(int $id, array $variables): bool
    {
        $data = [
            'css_variables' => json_encode($variables),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $affected = $this->database->update($this->table, $data, ['id' => $id]);
        return $affected > 0;
    }

    public function updateSettings(int $id, array $settings): bool
    {
        $data = [
            'settings' => json_encode($settings),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $affected = $this->database->update($this->table, $data, ['id' => $id]);
        return $affected > 0;
    }

    public function getCssVariables(int $id): array
    {
        $sql = "SELECT css_variables FROM {$this->table} WHERE id = :id LIMIT 1";
        $result = $this->database->fetch($sql, ['id' => $id]);
        
        if (!$result || empty($result['css_variables'])) {
            return [];
        }

        $variables = json_decode($result['css_variables'], true);
        return is_array($variables) ? $variables : [];
    }

    public function getSettings(int $id): array
    {
        $sql = "SELECT settings FROM {$this->table} WHERE id = :id LIMIT 1";
        $result = $this->database->fetch($sql, ['id' => $id]);
        
        if (!$result || empty($result['settings'])) {
            return [];
        }

        $settings = json_decode($result['settings'], true);
        return is_array($settings) ? $settings : [];
    }

    public function isSlugUnique(string $slug, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE slug = :slug";
        $params = ['slug' => $slug];

        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $result = $this->database->fetch($sql, $params);
        return (int) ($result['count'] ?? 0) === 0;
    }

    public function generateUniqueSlug(string $name, int $excludeId = null): string
    {
        $slug = $this->createSlug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (!$this->isSlugUnique($slug, $excludeId)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    public function getThemeStats(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_themes,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_themes,
                SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as default_themes
            FROM {$this->table}
        ";
        
        $result = $this->database->fetch($sql);
        
        return [
            'total' => (int) ($result['total_themes'] ?? 0),
            'active' => (int) ($result['active_themes'] ?? 0),
            'default' => (int) ($result['default_themes'] ?? 0)
        ];
    }
}
