<header class="site-header">
    <div class="container">
        <div class="header-content">
            <!-- Logo -->
            <div class="header-logo">
                <a href="/" class="logo-link">
                    <img src="<?= $this->asset('images/logo.png') ?>" alt="EticSimple" class="logo-image">
                    <span class="logo-text">EticSimple</span>
                </a>
            </div>
            
            <!-- Navigation -->
            <nav class="header-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="/" class="nav-link">Ana Sayfa</a>
                    </li>
                    <li class="nav-item">
                        <a href="/products" class="nav-link">Ürünler</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle">Kate<PERSON><PERSON></a>
                        <ul class="dropdown-menu">
                            <!-- <PERSON><PERSON><PERSON> buraya dinamik olarak yüklenecek -->
                            <li><a href="/category/elektronik" class="dropdown-link">Elektronik</a></li>
                            <li><a href="/category/giyim" class="dropdown-link">Giyim</a></li>
                            <li><a href="/category/ev-yasam" class="dropdown-link">Ev & Yaşam</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="/contact" class="nav-link">İletişim</a>
                    </li>
                </ul>
            </nav>
            
            <!-- Search -->
            <div class="header-search">
                <form action="/search" method="GET" class="search-form">
                    <input type="text" name="q" placeholder="Ürün ara..." class="search-input" value="<?= $this->e($_GET['q'] ?? '') ?>">
                    <button type="submit" class="search-button">
                        <i class="icon-search"></i>
                    </button>
                </form>
            </div>
            
            <!-- User Actions -->
            <div class="header-actions">
                <!-- Theme Selector -->
                <div class="theme-selector">
                    <button class="theme-toggle" title="Tema Değiştir">
                        <i class="icon-palette"></i>
                    </button>
                    <div class="theme-dropdown">
                        <button data-theme-selector="light" class="theme-option">
                            <span class="theme-preview light"></span>
                            Açık Tema
                        </button>
                        <button data-theme-selector="dark" class="theme-option">
                            <span class="theme-preview dark"></span>
                            Koyu Tema
                        </button>
                        <button data-theme-selector="blue" class="theme-option">
                            <span class="theme-preview blue"></span>
                            Mavi Tema
                        </button>
                        <button data-theme-selector="green" class="theme-option">
                            <span class="theme-preview green"></span>
                            Yeşil Tema
                        </button>
                    </div>
                </div>
                
                <!-- Cart -->
                <a href="/cart" class="cart-link">
                    <i class="icon-cart"></i>
                    <span class="cart-count" data-cart-count><?= $cartCount ?? 0 ?></span>
                </a>
                
                <!-- User Menu -->
                <?php if (isset($user) && $user): ?>
                <div class="user-menu">
                    <button class="user-toggle">
                        <i class="icon-user"></i>
                        <span class="user-name"><?= $this->e($user['first_name']) ?></span>
                    </button>
                    <div class="user-dropdown">
                        <a href="/account" class="dropdown-link">
                            <i class="icon-user"></i>
                            Hesabım
                        </a>
                        <a href="/account/orders" class="dropdown-link">
                            <i class="icon-package"></i>
                            Siparişlerim
                        </a>
                        <a href="/account/addresses" class="dropdown-link">
                            <i class="icon-map-pin"></i>
                            Adreslerim
                        </a>
                        <div class="dropdown-divider"></div>
                        <form action="/logout" method="POST" class="logout-form">
                            <input type="hidden" name="_token" value="<?= $this->csrf() ?>">
                            <button type="submit" class="dropdown-link logout-link">
                                <i class="icon-log-out"></i>
                                Çıkış Yap
                            </button>
                        </form>
                    </div>
                </div>
                <?php else: ?>
                <div class="auth-links">
                    <a href="/login" class="auth-link">Giriş</a>
                    <a href="/register" class="auth-link register">Kayıt Ol</a>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle">
                <span class="hamburger"></span>
            </button>
        </div>
    </div>
</header>

<style>
/* Header Styles */
.site-header {
    background-color: var(--theme-bg-primary);
    border-bottom: 1px solid var(--theme-border-primary);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    gap: 2rem;
}

.header-logo .logo-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.logo-image {
    height: 40px;
    width: auto;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--theme-accent-primary);
}

/* Navigation */
.header-nav {
    flex: 1;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--theme-text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: var(--theme-accent-primary);
}

/* Search */
.search-form {
    display: flex;
    position: relative;
}

.search-input {
    width: 300px;
    padding: 0.75rem 1rem;
    border: 2px solid var(--theme-border-primary);
    border-radius: var(--radius-md);
    background-color: var(--theme-bg-primary);
    color: var(--theme-text-primary);
}

.search-button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--theme-text-secondary);
    cursor: pointer;
}

/* Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-link {
    position: relative;
    color: var(--theme-text-primary);
    font-size: 1.25rem;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--theme-accent-primary);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Theme Selector */
.theme-selector {
    position: relative;
}

.theme-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem;
    min-width: 150px;
    display: none;
}

.theme-selector:hover .theme-dropdown {
    display: block;
}

.theme-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.5rem;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    border-radius: var(--radius-sm);
}

.theme-option:hover {
    background-color: var(--theme-bg-secondary);
}

.theme-preview {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid var(--theme-border-secondary);
}

.theme-preview.light { background: #ffffff; }
.theme-preview.dark { background: #111827; }
.theme-preview.blue { background: #3b82f6; }
.theme-preview.green { background: #22c55e; }

/* User Menu */
.user-menu {
    position: relative;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem;
    min-width: 200px;
    display: none;
}

.user-menu:hover .user-dropdown {
    display: block;
}

.dropdown-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    color: var(--theme-text-primary);
    text-decoration: none;
    border-radius: var(--radius-sm);
    width: 100%;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
}

.dropdown-link:hover {
    background-color: var(--theme-bg-secondary);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--theme-border-primary);
    margin: 0.5rem 0;
}

/* Auth Links */
.auth-links {
    display: flex;
    gap: 1rem;
}

.auth-link {
    color: var(--theme-text-primary);
    text-decoration: none;
    font-weight: 500;
}

.auth-link.register {
    background-color: var(--theme-accent-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
}

/* Mobile */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
}

@media (max-width: 768px) {
    .header-nav,
    .header-search {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .search-input {
        width: 200px;
    }
}
</style>
