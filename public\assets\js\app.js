/*
|--------------------------------------------------------------------------
| EticSimple JavaScript Framework
|--------------------------------------------------------------------------
| Monolitik e-ticaret sistemi için temel JavaScript
| Tema sistemi ve genel işlevsellik
|--------------------------------------------------------------------------
*/

// Theme Controller Class
class ThemeController {
    constructor() {
        this.currentTheme = this.getStoredTheme() || 'light';
        this.applyTheme(this.currentTheme);
        this.initializeThemeSelector();
    }

    applyTheme(themeName) {
        document.documentElement.setAttribute('data-theme', themeName);
        localStorage.setItem('selected-theme', themeName);
        this.currentTheme = themeName;
        this.notifyThemeChange(themeName);
    }

    getAvailableThemes() {
        return ['light', 'dark', 'blue', 'green'];
    }

    getStoredTheme() {
        return localStorage.getItem('selected-theme');
    }

    notifyThemeChange(themeName) {
        // Server'a tema değişikliğini bildir
        if (window.user && window.user.isLoggedIn) {
            fetch('/account/theme', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ theme: themeName })
            }).catch(error => {
                console.error('Theme update failed:', error);
            });
        }
    }

    initializeThemeSelector() {
        // Tema seçici butonlarını dinle
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-theme-selector]')) {
                const theme = e.target.getAttribute('data-theme-selector');
                this.applyTheme(theme);
            }
        });
    }
}

// Cart Manager Class
class CartManager {
    constructor() {
        this.initializeCartEvents();
        this.updateCartCount();
    }

    initializeCartEvents() {
        // Sepete ekleme
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-add-to-cart]')) {
                e.preventDefault();
                const productId = e.target.getAttribute('data-product-id');
                const quantity = e.target.getAttribute('data-quantity') || 1;
                this.addToCart(productId, quantity);
            }
        });

        // Sepetten çıkarma
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-remove-from-cart]')) {
                e.preventDefault();
                const productId = e.target.getAttribute('data-product-id');
                this.removeFromCart(productId);
            }
        });

        // Miktar güncelleme
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-cart-quantity]')) {
                const productId = e.target.getAttribute('data-product-id');
                const quantity = e.target.value;
                this.updateQuantity(productId, quantity);
            }
        });
    }

    async addToCart(productId, quantity = 1) {
        try {
            const response = await fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: quantity
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification('Ürün sepete eklendi!', 'success');
                this.updateCartCount();
            } else {
                this.showNotification(result.message || 'Bir hata oluştu', 'error');
            }
        } catch (error) {
            console.error('Add to cart error:', error);
            this.showNotification('Bir hata oluştu', 'error');
        }
    }

    async removeFromCart(productId) {
        try {
            const response = await fetch('/cart/remove', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: productId
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification('Ürün sepetten çıkarıldı', 'success');
                this.updateCartCount();
                // Sayfa yenileme (sepet sayfasındaysak)
                if (window.location.pathname === '/cart') {
                    window.location.reload();
                }
            } else {
                this.showNotification(result.message || 'Bir hata oluştu', 'error');
            }
        } catch (error) {
            console.error('Remove from cart error:', error);
            this.showNotification('Bir hata oluştu', 'error');
        }
    }

    async updateQuantity(productId, quantity) {
        try {
            const response = await fetch('/cart/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: quantity
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.updateCartCount();
                // Fiyat güncelleme (sepet sayfasındaysak)
                if (window.location.pathname === '/cart') {
                    this.updateCartTotals(result.cart);
                }
            } else {
                this.showNotification(result.message || 'Bir hata oluştu', 'error');
            }
        } catch (error) {
            console.error('Update cart error:', error);
            this.showNotification('Bir hata oluştu', 'error');
        }
    }

    updateCartCount() {
        // Sepet sayısını güncelle
        const cartCountElements = document.querySelectorAll('[data-cart-count]');
        // Bu fonksiyon server'dan sepet sayısını alabilir
        // Şimdilik basit bir implementasyon
    }

    updateCartTotals(cart) {
        // Sepet toplamlarını güncelle
        // Bu fonksiyon sepet sayfasında kullanılır
    }

    showNotification(message, type = 'info') {
        // Basit notification sistemi
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Stil ekle
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            animation: slideIn 0.3s ease;
        `;
        
        // Renk ayarla
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#22c55e';
                break;
            case 'error':
                notification.style.backgroundColor = '#ef4444';
                break;
            case 'warning':
                notification.style.backgroundColor = '#f59e0b';
                break;
            default:
                notification.style.backgroundColor = '#3b82f6';
        }
        
        document.body.appendChild(notification);
        
        // 3 saniye sonra kaldır
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Form Validation Helper
class FormValidator {
    static validateTCKimlik(tcKimlik) {
        if (!tcKimlik || tcKimlik.length !== 11) return false;
        
        const digits = tcKimlik.split('').map(Number);
        
        // İlk hane 0 olamaz
        if (digits[0] === 0) return false;
        
        // Checksum kontrolü
        const sum1 = digits[0] + digits[2] + digits[4] + digits[6] + digits[8];
        const sum2 = digits[1] + digits[3] + digits[5] + digits[7];
        
        const check1 = (sum1 * 7 - sum2) % 10;
        const check2 = (sum1 + sum2 + digits[9]) % 10;
        
        return check1 === digits[9] && check2 === digits[10];
    }

    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    static validatePhone(phone) {
        const phoneRegex = /^(\+90|0)?[5][0-9]{9}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize managers
    window.cartManager = new CartManager();
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
});

// Global utilities
window.EticSimple = {
    ThemeController,
    CartManager,
    FormValidator
};
