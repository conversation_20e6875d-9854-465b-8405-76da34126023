<?php

namespace Core\Router;

class Router
{
    private array $routes = [];
    private array $middleware = [];
    private string $currentMethod;
    private string $currentUri;
    private string $currentPrefix = '';
    private array $currentMiddleware = [];

    public function __construct()
    {
        $this->currentMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $this->currentUri = $this->parseUri();
    }

    private function parseUri(): string
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        
        // Remove query string
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }
        
        // Remove trailing slash except for root
        if ($uri !== '/' && substr($uri, -1) === '/') {
            $uri = rtrim($uri, '/');
        }
        
        return $uri;
    }

    public function get(string $uri, $handler, array $middleware = []): void
    {
        $this->addRoute('GET', $uri, $handler, $middleware);
    }

    public function post(string $uri, $handler, array $middleware = []): void
    {
        $this->addRoute('POST', $uri, $handler, $middleware);
    }

    public function put(string $uri, $handler, array $middleware = []): void
    {
        $this->addRoute('PUT', $uri, $handler, $middleware);
    }

    public function delete(string $uri, $handler, array $middleware = []): void
    {
        $this->addRoute('DELETE', $uri, $handler, $middleware);
    }

    /**
     * Route grubu oluştur
     */
    public function group(array $attributes, callable $callback): void
    {
        $prefix = $attributes['prefix'] ?? '';
        $middleware = $attributes['middleware'] ?? [];

        // Middleware'i array'e çevir
        if (is_string($middleware)) {
            $middleware = [$middleware];
        }

        // Geçici olarak prefix ve middleware'i sakla
        $originalPrefix = $this->currentPrefix ?? '';
        $originalMiddleware = $this->currentMiddleware ?? [];

        $this->currentPrefix = $originalPrefix . $prefix;
        $this->currentMiddleware = array_merge($originalMiddleware, $middleware);

        // Callback'i çalıştır
        $callback($this);

        // Orijinal değerleri geri yükle
        $this->currentPrefix = $originalPrefix;
        $this->currentMiddleware = $originalMiddleware;
    }

    private function addRoute(string $method, string $uri, $handler, array $middleware): void
    {
        // Prefix ekle
        $uri = $this->currentPrefix . $uri;

        // Middleware'leri birleştir
        $allMiddleware = array_merge($this->currentMiddleware, $middleware);

        // Normalize URI
        if ($uri !== '/' && substr($uri, -1) === '/') {
            $uri = rtrim($uri, '/');
        }

        $this->routes[$method][$uri] = [
            'handler' => $handler,
            'middleware' => $allMiddleware,
            'pattern' => $this->convertToPattern($uri)
        ];
    }

    private function convertToPattern(string $uri): string
    {
        // Convert {param} to regex pattern
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $uri);
        return '#^' . $pattern . '$#';
    }

    public function handleRequest(): void
    {
        $route = $this->findRoute();
        
        if (!$route) {
            $this->handleNotFound();
            return;
        }

        // Execute middleware
        if (!$this->executeMiddleware($route['middleware'])) {
            return; // Middleware blocked the request
        }

        // Execute handler
        $this->executeHandler($route['handler'], $route['params'] ?? []);
    }

    private function findRoute(): ?array
    {
        if (!isset($this->routes[$this->currentMethod])) {
            return null;
        }

        foreach ($this->routes[$this->currentMethod] as $uri => $route) {
            // Exact match first
            if ($uri === $this->currentUri) {
                return $route;
            }

            // Pattern match for dynamic routes
            if (preg_match($route['pattern'], $this->currentUri, $matches)) {
                array_shift($matches); // Remove full match
                $route['params'] = $matches;
                return $route;
            }
        }

        return null;
    }

    private function executeMiddleware(array $middlewares): bool
    {
        foreach ($middlewares as $middlewareClass) {
            if (is_string($middlewareClass)) {
                if (!class_exists($middlewareClass)) {
                    throw new \Exception("Middleware {$middlewareClass} not found");
                }

                $middleware = new $middlewareClass();

                if (!method_exists($middleware, 'handle')) {
                    throw new \Exception("Middleware {$middlewareClass} must have a handle method");
                }

                if (!$middleware->handle()) {
                    return false;
                }
            } elseif (is_callable($middlewareClass)) {
                if (!$middlewareClass()) {
                    return false;
                }
            }
        }

        return true;
    }

    private function executeHandler($handler, array $params = []): void
    {
        if (is_array($handler)) {
            // Handle [ControllerClass::class, 'method'] format
            [$controllerClass, $method] = $handler;

            if (!class_exists($controllerClass)) {
                throw new \Exception("Controller {$controllerClass} not found");
            }

            $controller = new $controllerClass();

            if (!method_exists($controller, $method)) {
                throw new \Exception("Method {$method} not found in {$controllerClass}");
            }

            call_user_func_array([$controller, $method], $params);
        } elseif (is_string($handler)) {
            // Handle "Controller@method" format
            if (strpos($handler, '@') !== false) {
                [$controllerClass, $method] = explode('@', $handler);
                
                if (!class_exists($controllerClass)) {
                    throw new \Exception("Controller {$controllerClass} not found");
                }

                $controller = new $controllerClass();
                
                if (!method_exists($controller, $method)) {
                    throw new \Exception("Method {$method} not found in {$controllerClass}");
                }

                call_user_func_array([$controller, $method], $params);
            } else {
                // Handle function name
                if (!function_exists($handler)) {
                    throw new \Exception("Function {$handler} not found");
                }
                call_user_func_array($handler, $params);
            }
        } elseif (is_callable($handler)) {
            // Handle closure
            call_user_func_array($handler, $params);
        } else {
            throw new \Exception("Invalid route handler");
        }
    }

    private function handleNotFound(): void
    {
        http_response_code(404);
        
        // Check if it's an admin route
        if (strpos($this->currentUri, '/admin') === 0) {
            $this->render404('admin');
        } else {
            $this->render404('store');
        }
    }

    private function render404(string $type): void
    {
        $templatePath = __DIR__ . "/../../views/{$type}/pages/404.php";
        
        if (file_exists($templatePath)) {
            include $templatePath;
        } else {
            echo "<h1>404 - Sayfa Bulunamadı</h1>";
            echo "<p>Aradığınız sayfa bulunamadı.</p>";
        }
    }

    public function redirect(string $url, int $statusCode = 302): void
    {
        header("Location: {$url}", true, $statusCode);
        exit;
    }

    public function getCurrentUri(): string
    {
        return $this->currentUri;
    }

    public function getCurrentMethod(): string
    {
        return $this->currentMethod;
    }
}
