<?php

namespace App\Services;

use App\Repositories\ThemeRepository;
use Core\Application;

class ThemeService
{
    private ThemeRepository $themeRepository;
    private array $defaultThemes;

    public function __construct()
    {
        $app = Application::getInstance();
        $this->themeRepository = new ThemeRepository($app->getDatabase());
        $this->initializeDefaultThemes();
    }

    private function initializeDefaultThemes(): void
    {
        $this->defaultThemes = [
            'light' => [
                'name' => 'Light Theme',
                'slug' => 'light',
                'description' => 'Açık renk teması',
                'css_variables' => [
                    'theme-bg-primary' => '#ffffff',
                    'theme-bg-secondary' => '#f9fafb',
                    'theme-bg-tertiary' => '#f3f4f6',
                    'theme-text-primary' => '#111827',
                    'theme-text-secondary' => '#6b7280',
                    'theme-text-muted' => '#9ca3af',
                    'theme-border-primary' => '#e5e7eb',
                    'theme-border-secondary' => '#d1d5db',
                    'theme-accent-primary' => '#0ea5e9',
                    'theme-accent-secondary' => '#d946ef',
                ]
            ],
            'dark' => [
                'name' => 'Dark Theme',
                'slug' => 'dark',
                'description' => 'Koyu renk teması',
                'css_variables' => [
                    'theme-bg-primary' => '#111827',
                    'theme-bg-secondary' => '#1f2937',
                    'theme-bg-tertiary' => '#374151',
                    'theme-text-primary' => '#f9fafb',
                    'theme-text-secondary' => '#d1d5db',
                    'theme-text-muted' => '#9ca3af',
                    'theme-border-primary' => '#374151',
                    'theme-border-secondary' => '#4b5563',
                    'theme-accent-primary' => '#0ea5e9',
                    'theme-accent-secondary' => '#d946ef',
                ]
            ],
            'blue' => [
                'name' => 'Blue Theme',
                'slug' => 'blue',
                'description' => 'Mavi kurumsal tema',
                'css_variables' => [
                    'theme-bg-primary' => '#f8fafc',
                    'theme-bg-secondary' => '#f1f5f9',
                    'theme-bg-tertiary' => '#e2e8f0',
                    'theme-text-primary' => '#0f172a',
                    'theme-text-secondary' => '#475569',
                    'theme-text-muted' => '#64748b',
                    'theme-border-primary' => '#cbd5e1',
                    'theme-border-secondary' => '#94a3b8',
                    'theme-accent-primary' => '#3b82f6',
                    'theme-accent-secondary' => '#1e40af',
                ]
            ],
            'green' => [
                'name' => 'Green Theme',
                'slug' => 'green',
                'description' => 'Yeşil doğa teması',
                'css_variables' => [
                    'theme-bg-primary' => '#f7fdf7',
                    'theme-bg-secondary' => '#f0fdf4',
                    'theme-bg-tertiary' => '#dcfce7',
                    'theme-text-primary' => '#14532d',
                    'theme-text-secondary' => '#166534',
                    'theme-text-muted' => '#22c55e',
                    'theme-border-primary' => '#bbf7d0',
                    'theme-border-secondary' => '#86efac',
                    'theme-accent-primary' => '#22c55e',
                    'theme-accent-secondary' => '#16a34a',
                ]
            ]
        ];
    }

    public function getCurrentTheme(): array
    {
        // Kullanıcı giriş yapmışsa kullanıcının temasını al
        $app = Application::getInstance();
        $auth = $app->getAuthManager();
        
        if ($auth->check()) {
            $user = $auth->user();
            if (!empty($user['theme'])) {
                $theme = $this->getThemeBySlug($user['theme']);
                if ($theme) {
                    return $theme;
                }
            }
        }

        // Session'dan tema al
        $session = $app->getSessionManager();
        $themeSlug = $session->get('theme');
        
        if ($themeSlug) {
            $theme = $this->getThemeBySlug($themeSlug);
            if ($theme) {
                return $theme;
            }
        }

        // Varsayılan tema
        $defaultTheme = $app->getConfig('app.theme.default') ?? 'light';
        return $this->getThemeBySlug($defaultTheme) ?? $this->defaultThemes['light'];
    }

    public function getThemeBySlug(string $slug): ?array
    {
        // Önce veritabanından kontrol et
        $theme = $this->themeRepository->findBySlug($slug);
        if ($theme) {
            return $theme;
        }

        // Varsayılan temalardan kontrol et
        return $this->defaultThemes[$slug] ?? null;
    }

    public function getAllThemes(): array
    {
        $dbThemes = $this->themeRepository->getAll();
        $allThemes = array_merge(array_values($this->defaultThemes), $dbThemes);
        
        return $allThemes;
    }

    public function setUserTheme(int $userId, string $themeSlug): bool
    {
        // Tema var mı kontrol et
        $theme = $this->getThemeBySlug($themeSlug);
        if (!$theme) {
            return false;
        }

        // Kullanıcının temasını güncelle
        $app = Application::getInstance();
        $database = $app->getDatabase();
        
        try {
            $database->update('users', ['theme' => $themeSlug], ['id' => $userId]);
            return true;
        } catch (\Exception $e) {
            error_log("Theme update error: " . $e->getMessage());
            return false;
        }
    }

    public function setSessionTheme(string $themeSlug): bool
    {
        // Tema var mı kontrol et
        $theme = $this->getThemeBySlug($themeSlug);
        if (!$theme) {
            return false;
        }

        // Session'a kaydet
        $app = Application::getInstance();
        $session = $app->getSessionManager();
        $session->set('theme', $themeSlug);
        
        return true;
    }

    public function activateTheme(string $themeSlug): bool
    {
        // Tema var mı kontrol et
        $theme = $this->getThemeBySlug($themeSlug);
        if (!$theme) {
            return false;
        }

        try {
            // Tüm temaları pasif yap
            $this->themeRepository->deactivateAll();
            
            // Seçilen temayı aktif yap
            if (isset($this->defaultThemes[$themeSlug])) {
                // Varsayılan tema ise veritabanına ekle
                $this->createDefaultTheme($themeSlug);
            }
            
            $this->themeRepository->activate($themeSlug);
            
            return true;
        } catch (\Exception $e) {
            error_log("Theme activation error: " . $e->getMessage());
            return false;
        }
    }

    private function createDefaultTheme(string $slug): void
    {
        if (!isset($this->defaultThemes[$slug])) {
            return;
        }

        $theme = $this->defaultThemes[$slug];
        
        // Tema zaten var mı kontrol et
        if ($this->themeRepository->findBySlug($slug)) {
            return;
        }

        // Temayı veritabanına ekle
        $this->themeRepository->create([
            'name' => $theme['name'],
            'slug' => $theme['slug'],
            'description' => $theme['description'],
            'version' => '1.0.0',
            'author' => 'EticSimple',
            'css_variables' => json_encode($theme['css_variables']),
            'is_active' => false,
            'is_default' => $slug === 'light'
        ]);
    }

    public function generateCssVariables(array $theme): string
    {
        $css = ":root {\n";
        
        if (isset($theme['css_variables'])) {
            $variables = is_string($theme['css_variables']) 
                ? json_decode($theme['css_variables'], true) 
                : $theme['css_variables'];
                
            foreach ($variables as $key => $value) {
                $css .= "    --{$key}: {$value};\n";
            }
        }
        
        $css .= "}\n";
        
        return $css;
    }

    public function getThemePreviewData(string $themeSlug): array
    {
        $theme = $this->getThemeBySlug($themeSlug);
        
        if (!$theme) {
            return [];
        }

        return [
            'theme' => $theme,
            'css' => $this->generateCssVariables($theme),
            'preview_components' => [
                'buttons' => true,
                'cards' => true,
                'forms' => true,
                'navigation' => true
            ]
        ];
    }
}
