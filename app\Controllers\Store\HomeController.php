<?php

namespace App\Controllers\Store;

use App\Controllers\BaseController;
use App\Services\ProductService;
use App\Services\ThemeService;

class HomeController extends BaseController
{
    private ProductService $productService;
    private ThemeService $themeService;

    public function __construct()
    {
        parent::__construct();
        $this->productService = new ProductService();
        $this->themeService = new ThemeService();
    }

    public function index(): void
    {
        try {
            // Ana sayfa için gerekli veriler
            $featuredProducts = $this->productService->getFeaturedProducts(8);
            $categories = $this->productService->getMainCategories();
            $currentTheme = $this->themeService->getCurrentTheme();
            
            // Slider/Banner verilerini buraya ekleyebiliriz
            $banners = []; // Şimdilik boş
            
            $data = [
                'title' => 'Ana Sayfa',
                'featuredProducts' => $featuredProducts,
                'categories' => $categories,
                'banners' => $banners,
                'currentTheme' => $currentTheme,
                'user' => $this->user(),
                'cartCount' => $this->getCartCount(),
            ];

            $this->view('store/pages/home', $data);
            
        } catch (\Exception $e) {
            // Log error
            error_log("Home page error: " . $e->getMessage());
            
            // Show error page or fallback
            $this->view('store/pages/error', [
                'title' => 'Hata',
                'message' => 'Ana sayfa yüklenirken bir hata oluştu.',
                'user' => $this->user(),
            ]);
        }
    }

    private function getCartCount(): int
    {
        // Session'dan sepet sayısını al
        $session = $this->app->getSessionManager();
        $cart = $session->get('cart', []);
        
        $count = 0;
        foreach ($cart as $item) {
            $count += $item['quantity'] ?? 0;
        }
        
        return $count;
    }
}
