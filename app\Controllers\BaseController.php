<?php

namespace App\Controllers;

use Core\Application;

abstract class BaseController
{
    protected Application $app;

    public function __construct()
    {
        $this->app = Application::getInstance();
    }

    protected function view(string $template, array $data = []): void
    {
        $templateEngine = $this->app->getTemplateEngine();
        $templateEngine->render($template, $data);
    }

    protected function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    protected function redirect(string $url, int $statusCode = 302): void
    {
        header("Location: {$url}", true, $statusCode);
        exit;
    }

    protected function back(): void
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '/';
        $this->redirect($referer);
    }

    protected function getInput(string $key = null, $default = null)
    {
        if ($key === null) {
            return $_POST + $_GET;
        }

        return $_POST[$key] ?? $_GET[$key] ?? $default;
    }

    protected function validate(array $rules): array
    {
        $validator = new \Core\Validation\Validator();
        return $validator->validate($this->getInput(), $rules);
    }

    protected function isPost(): bool
    {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }

    protected function isGet(): bool
    {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }

    protected function isAjax(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    protected function csrf(): string
    {
        $session = $this->app->getSessionManager();
        return $session->getCsrfToken();
    }

    protected function verifyCsrf(): bool
    {
        $session = $this->app->getSessionManager();
        $token = $this->getInput('_token');
        return $session->verifyCsrfToken($token);
    }

    protected function flash(string $key, string $message): void
    {
        $session = $this->app->getSessionManager();
        $session->flash($key, $message);
    }

    protected function getFlash(string $key): ?string
    {
        $session = $this->app->getSessionManager();
        return $session->getFlash($key);
    }

    protected function auth()
    {
        return $this->app->getAuthManager();
    }

    protected function user()
    {
        return $this->auth()->user();
    }

    protected function isLoggedIn(): bool
    {
        return $this->auth()->check();
    }

    protected function isAdmin(): bool
    {
        return $this->auth()->isAdmin();
    }

    protected function requireAuth(): void
    {
        if (!$this->isLoggedIn()) {
            $this->redirect('/login');
        }
    }

    protected function requireAdmin(): void
    {
        if (!$this->isAdmin()) {
            $this->redirect('/admin/login');
        }
    }
}
