<?php

/*
|--------------------------------------------------------------------------
| EticSimple <PERSON>-<PERSON><PERSON><PERSON>
|--------------------------------------------------------------------------
| Türkiye odaklı monolitik e-ticaret sistemi
| Giriş noktası (Entry Point)
|--------------------------------------------------------------------------
*/

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define paths
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/app');
define('CORE_PATH', ROOT_PATH . '/core');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('PUBLIC_PATH', __DIR__);

// Autoloader
require_once ROOT_PATH . '/vendor/autoload.php';

// Start application
try {
    $app = \Core\Application::getInstance();
    
    // Load routes
    require_once ROOT_PATH . '/routes.php';
    
    // Run application
    $app->run();
    
} catch (\Exception $e) {
    // Emergency error handling
    if (defined('APP_DEBUG') && APP_DEBUG) {
        echo "<h1>Fatal Error</h1>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        error_log("Fatal error: " . $e->getMessage());
        http_response_code(500);
        echo "Sistem geçici olarak kullanılamıyor. Lütfen daha sonra tekrar deneyin.";
    }
}
