<?php $this->extends('admin.layouts.app'); ?>

<?php $this->section('content'); ?>

<!-- Dashboard Stats -->
<div class="dashboard-stats">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="icon-shopping-bag"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?= number_format($stats['total_orders']) ?></div>
            <div class="stat-label">Toplam Sipariş</div>
            <div class="stat-change positive">
                +<?= number_format($stats['monthly_orders']) ?> bu ay
            </div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="icon-users"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?= number_format($stats['total_customers']) ?></div>
            <div class="stat-label">Toplam Müşteri</div>
            <div class="stat-change">
                Akt<PERSON>
            </div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="icon-package"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?= number_format($stats['total_products']) ?></div>
            <div class="stat-label">Toplam Ürün</div>
            <div class="stat-change">
                Aktif ürünler
            </div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="icon-dollar-sign"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?= $this->formatPrice($stats['monthly_revenue']) ?></div>
            <div class="stat-label">Bu Ayki Gelir</div>
            <div class="stat-change <?= $stats['pending_orders'] > 0 ? 'warning' : 'positive' ?>">
                <?= $stats['pending_orders'] ?> bekleyen sipariş
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Content -->
<div class="dashboard-content">
    <!-- Sales Chart -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">Son 30 Gün Satış Grafiği</h3>
            <div class="card-actions">
                <button class="btn btn-sm btn-outline">Rapor Al</button>
            </div>
        </div>
        <div class="card-body">
            <canvas id="salesChart" width="400" height="200"></canvas>
        </div>
    </div>
    
    <!-- Recent Orders -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">Son Siparişler</h3>
            <div class="card-actions">
                <a href="/admin/orders" class="btn btn-sm btn-outline">Tümünü Gör</a>
            </div>
        </div>
        <div class="card-body">
            <?php if (!empty($recentOrders)): ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Sipariş No</th>
                            <th>Müşteri</th>
                            <th>Tutar</th>
                            <th>Durum</th>
                            <th>Tarih</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentOrders as $order): ?>
                        <tr>
                            <td>
                                <a href="/admin/orders/<?= $order['id'] ?>" class="order-link">
                                    #<?= $order['order_number'] ?? $order['id'] ?>
                                </a>
                            </td>
                            <td>
                                <?= $this->e(($order['first_name'] ?? '') . ' ' . ($order['last_name'] ?? '')) ?>
                                <br>
                                <small class="text-muted"><?= $this->e($order['email'] ?? '') ?></small>
                            </td>
                            <td><?= $this->formatPrice($order['total_amount'] ?? 0) ?></td>
                            <td>
                                <span class="status-badge status-<?= $order['status'] ?? 'pending' ?>">
                                    <?= ucfirst($order['status'] ?? 'pending') ?>
                                </span>
                            </td>
                            <td><?= $this->formatDate($order['created_at'] ?? '') ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="empty-state">
                <i class="icon-shopping-bag"></i>
                <p>Henüz sipariş bulunmuyor.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Secondary Content -->
<div class="dashboard-secondary">
    <!-- Low Stock Products -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">Düşük Stoklu Ürünler</h3>
            <div class="card-actions">
                <a href="/admin/products?filter=low_stock" class="btn btn-sm btn-outline">Tümünü Gör</a>
            </div>
        </div>
        <div class="card-body">
            <?php if (!empty($lowStockProducts)): ?>
            <div class="product-list">
                <?php foreach ($lowStockProducts as $product): ?>
                <div class="product-item">
                    <div class="product-info">
                        <div class="product-name"><?= $this->e($product['name']) ?></div>
                        <div class="product-sku">SKU: <?= $this->e($product['sku']) ?></div>
                    </div>
                    <div class="product-stock">
                        <span class="stock-count <?= $product['inventory_quantity'] <= 2 ? 'critical' : 'warning' ?>">
                            <?= $product['inventory_quantity'] ?> adet
                        </span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <div class="empty-state">
                <i class="icon-check-circle"></i>
                <p>Tüm ürünler yeterli stokta.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">Hızlı İşlemler</h3>
        </div>
        <div class="card-body">
            <div class="quick-actions">
                <a href="/admin/products/create" class="quick-action">
                    <i class="icon-plus"></i>
                    <span>Yeni Ürün Ekle</span>
                </a>
                <a href="/admin/orders" class="quick-action">
                    <i class="icon-package"></i>
                    <span>Siparişleri Görüntüle</span>
                </a>
                <a href="/admin/users" class="quick-action">
                    <i class="icon-users"></i>
                    <span>Müşteri Yönetimi</span>
                </a>
                <a href="/admin/settings" class="quick-action">
                    <i class="icon-settings"></i>
                    <span>Site Ayarları</span>
                </a>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->section('styles'); ?>
<style>
/* Dashboard Styles */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-primary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--theme-accent-primary), var(--theme-accent-secondary));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--theme-text-primary);
    line-height: 1;
}

.stat-label {
    color: var(--theme-text-secondary);
    font-weight: 500;
    margin: 0.25rem 0;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-change.positive { color: var(--theme-success); }
.stat-change.warning { color: var(--theme-warning); }

.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-secondary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.dashboard-card {
    background: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--theme-border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--theme-text-primary);
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--theme-border-primary);
}

.table th {
    font-weight: 600;
    color: var(--theme-text-primary);
    background: var(--theme-bg-secondary);
}

.order-link {
    color: var(--theme-accent-primary);
    text-decoration: none;
    font-weight: 500;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: var(--theme-warning); color: white; }
.status-processing { background: var(--theme-info); color: white; }
.status-shipped { background: var(--theme-success); color: white; }
.status-delivered { background: var(--theme-success); color: white; }
.status-cancelled { background: var(--theme-error); color: white; }

.product-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--theme-bg-secondary);
    border-radius: var(--radius-md);
}

.product-name {
    font-weight: 500;
    color: var(--theme-text-primary);
}

.product-sku {
    font-size: 0.875rem;
    color: var(--theme-text-muted);
}

.stock-count {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
}

.stock-count.warning {
    background: var(--theme-warning);
    color: white;
}

.stock-count.critical {
    background: var(--theme-error);
    color: white;
}

.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--theme-bg-secondary);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--theme-text-primary);
    transition: all 0.2s ease;
}

.quick-action:hover {
    background: var(--theme-accent-primary);
    color: white;
    transform: translateY(-2px);
}

.quick-action i {
    font-size: 1.5rem;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--theme-text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .dashboard-content,
    .dashboard-secondary {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}
</style>
<?php $this->endSection(); ?>

<?php $this->section('scripts'); ?>
<script>
// Sales Chart (placeholder - Chart.js kullanılabilir)
document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('salesChart');
    if (canvas) {
        const ctx = canvas.getContext('2d');
        
        // Simple chart placeholder
        ctx.fillStyle = '#f3f4f6';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        ctx.fillStyle = '#6b7280';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Satış Grafiği', canvas.width / 2, canvas.height / 2);
        ctx.fillText('(Chart.js entegrasyonu gerekli)', canvas.width / 2, canvas.height / 2 + 25);
    }
});
</script>
<?php $this->endSection(); ?>
