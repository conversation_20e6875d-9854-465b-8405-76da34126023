<?php

namespace Core;

use Core\Router\Router;
use Core\Database\Database;
use Core\Template\TemplateEngine;
use Core\Auth\AuthManager;
use Core\Session\SessionManager;
use Core\Cache\CacheManager;
use Dotenv\Dotenv;

class Application
{
    private static ?Application $instance = null;
    private array $config = [];
    private Router $router;
    private Database $database;
    private TemplateEngine $templateEngine;
    private AuthManager $authManager;
    private SessionManager $sessionManager;
    private CacheManager $cacheManager;

    private function __construct()
    {
        $this->loadEnvironment();
        $this->loadConfiguration();
        $this->initializeServices();
    }

    public static function getInstance(): Application
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadEnvironment(): void
    {
        if (file_exists(__DIR__ . '/../.env')) {
            $dotenv = Dotenv::createImmutable(__DIR__ . '/..');
            $dotenv->load();
        }
    }

    private function loadConfiguration(): void
    {
        $configFiles = ['app', 'database'];
        
        foreach ($configFiles as $file) {
            $configPath = __DIR__ . "/../config/{$file}.php";
            if (file_exists($configPath)) {
                $this->config[$file] = require $configPath;
            }
        }
    }

    private function initializeServices(): void
    {
        // Session Manager
        $this->sessionManager = new SessionManager($this->config['app']['security']);
        
        // Database
        $this->database = new Database($this->config['database']);
        
        // Cache Manager
        $this->cacheManager = new CacheManager($this->config['database']['redis']);
        
        // Template Engine
        $this->templateEngine = new TemplateEngine();
        
        // Auth Manager
        $this->authManager = new AuthManager($this->database, $this->sessionManager);
        
        // Router
        $this->router = new Router();
    }

    public function run(): void
    {
        try {
            // Start session
            $this->sessionManager->start();
            
            // Handle request
            $this->router->handleRequest();
            
        } catch (\Exception $e) {
            $this->handleException($e);
        }
    }

    private function handleException(\Exception $e): void
    {
        if ($this->config['app']['debug']) {
            echo "<h1>Error</h1>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        } else {
            // Log error
            error_log($e->getMessage());
            
            // Show generic error page
            http_response_code(500);
            echo "Bir hata oluştu. Lütfen daha sonra tekrar deneyin.";
        }
    }

    // Getter methods
    public function getConfig(string $key = null)
    {
        if ($key === null) {
            return $this->config;
        }
        
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return null;
            }
            $value = $value[$k];
        }
        
        return $value;
    }

    public function getRouter(): Router
    {
        return $this->router;
    }

    public function getDatabase(): Database
    {
        return $this->database;
    }

    public function getTemplateEngine(): TemplateEngine
    {
        return $this->templateEngine;
    }

    public function getAuthManager(): AuthManager
    {
        return $this->authManager;
    }

    public function getSessionManager(): SessionManager
    {
        return $this->sessionManager;
    }

    public function getCacheManager(): CacheManager
    {
        return $this->cacheManager;
    }
}
