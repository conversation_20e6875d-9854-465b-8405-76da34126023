<?php

namespace Core\Session;

class SessionManager
{
    private array $config;
    private bool $started = false;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'lifetime' => 120, // minutes
            'path' => '/',
            'domain' => '',
            'secure' => false,
            'httponly' => true,
            'samesite' => 'Lax'
        ], $config);
    }

    public function start(): bool
    {
        if ($this->started) {
            return true;
        }

        // Configure session
        ini_set('session.gc_maxlifetime', $this->config['lifetime'] * 60);
        ini_set('session.cookie_lifetime', $this->config['lifetime'] * 60);
        ini_set('session.cookie_httponly', $this->config['httponly'] ? '1' : '0');
        ini_set('session.cookie_secure', $this->config['secure'] ? '1' : '0');
        ini_set('session.cookie_samesite', $this->config['samesite']);

        if (!empty($this->config['domain'])) {
            ini_set('session.cookie_domain', $this->config['domain']);
        }

        ini_set('session.cookie_path', $this->config['path']);

        // Start session
        if (session_status() === PHP_SESSION_NONE) {
            $this->started = session_start();
        } else {
            $this->started = true;
        }

        // Initialize CSRF token if not exists
        if (!$this->has('_csrf_token')) {
            $this->regenerateCsrfToken();
        }

        return $this->started;
    }

    public function destroy(): bool
    {
        if (!$this->started) {
            return false;
        }

        $_SESSION = [];

        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params['path'],
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
        }

        $this->started = false;
        return session_destroy();
    }

    public function regenerate(bool $deleteOldSession = true): bool
    {
        if (!$this->started) {
            return false;
        }

        return session_regenerate_id($deleteOldSession);
    }

    public function get(string $key, $default = null)
    {
        if (!$this->started) {
            return $default;
        }

        return $_SESSION[$key] ?? $default;
    }

    public function set(string $key, $value): void
    {
        if ($this->started) {
            $_SESSION[$key] = $value;
        }
    }

    public function has(string $key): bool
    {
        return $this->started && isset($_SESSION[$key]);
    }

    public function remove(string $key): void
    {
        if ($this->started && isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }

    public function flash(string $key, string $message): void
    {
        if (!$this->started) {
            return;
        }

        if (!isset($_SESSION['_flash'])) {
            $_SESSION['_flash'] = [];
        }

        $_SESSION['_flash'][$key] = $message;
    }

    public function getFlash(string $key, string $default = ''): string
    {
        if (!$this->started || !isset($_SESSION['_flash'][$key])) {
            return $default;
        }

        $message = $_SESSION['_flash'][$key];
        unset($_SESSION['_flash'][$key]);

        return $message;
    }

    public function getAllFlash(): array
    {
        if (!$this->started || !isset($_SESSION['_flash'])) {
            return [];
        }

        $messages = $_SESSION['_flash'];
        $_SESSION['_flash'] = [];

        return $messages;
    }

    public function hasFlash(string $key): bool
    {
        return $this->started && isset($_SESSION['_flash'][$key]);
    }

    public function getCsrfToken(): string
    {
        if (!$this->has('_csrf_token')) {
            $this->regenerateCsrfToken();
        }

        return $this->get('_csrf_token');
    }

    public function regenerateCsrfToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $this->set('_csrf_token', $token);
        $this->set('_csrf_token_time', time());
        return $token;
    }

    public function verifyCsrfToken(string $token): bool
    {
        if (!$this->has('_csrf_token') || !$this->has('_csrf_token_time')) {
            return false;
        }

        $sessionToken = $this->get('_csrf_token');
        $tokenTime = $this->get('_csrf_token_time');

        // Check if token is expired (1 hour)
        if (time() - $tokenTime > 3600) {
            $this->regenerateCsrfToken();
            return false;
        }

        return hash_equals($sessionToken, $token);
    }

    public function old(string $key, $default = null)
    {
        return $this->get('_old')[$key] ?? $default;
    }

    public function flashInput(array $input): void
    {
        $this->set('_old', $input);
    }

    public function getOldInput(): array
    {
        $old = $this->get('_old', []);
        $this->remove('_old');
        return $old;
    }

    public function isStarted(): bool
    {
        return $this->started;
    }

    public function getId(): string
    {
        return $this->started ? session_id() : '';
    }

    public function getName(): string
    {
        return session_name();
    }

    public function all(): array
    {
        return $this->started ? $_SESSION : [];
    }

    public function clear(): void
    {
        if ($this->started) {
            $_SESSION = [];
        }
    }

    public function save(): void
    {
        if ($this->started) {
            session_write_close();
        }
    }
}
