{"name": "phpunit/php-invoker", "description": "Invoke callables with a timeout", "type": "library", "keywords": ["process"], "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues"}, "prefer-stable": true, "config": {"platform": {"php": "8.1.0"}, "optimize-autoloader": true, "sort-packages": true}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture/"]}, "suggest": {"ext-pcntl": "*"}, "extra": {"branch-alias": {"dev-main": "4.0-dev"}}}