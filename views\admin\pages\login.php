<!DOCTYPE html>
<html lang="tr" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= $this->csrf() ?>">
    
    <title><?= $this->e($title ?? 'Admin <PERSON>') ?> - EticSimple</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= $this->asset('images/favicon.ico') ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?= $this->asset('css/app.css') ?>">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-primary);
        }
        
        .login-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 2rem;
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--theme-accent-primary), var(--theme-accent-secondary));
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .login-subtitle {
            opacity: 0.9;
            font-size: 0.875rem;
        }
        
        .login-form {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--theme-text-primary);
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--theme-accent-primary);
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-checkbox input {
            width: auto;
        }
        
        .login-button {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, var(--theme-accent-primary), var(--theme-accent-secondary));
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .login-button:hover {
            transform: translateY(-1px);
        }
        
        .login-footer {
            padding: 1rem 2rem 2rem;
            text-align: center;
            border-top: 1px solid #e2e8f0;
            margin-top: 1rem;
        }
        
        .back-link {
            color: var(--theme-text-secondary);
            text-decoration: none;
            font-size: 0.875rem;
        }
        
        .back-link:hover {
            color: var(--theme-accent-primary);
        }
        
        .alert {
            padding: 0.75rem 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }
        
        .alert-error {
            background-color: #fee2e2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .alert-success {
            background-color: #d1fae5;
            color: #059669;
            border: 1px solid #a7f3d0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Header -->
        <div class="login-header">
            <div class="login-logo">EticSimple</div>
            <div class="login-subtitle">Admin Panel Girişi</div>
        </div>
        
        <!-- Form -->
        <form class="login-form" action="/admin/login" method="POST">
            <input type="hidden" name="_token" value="<?= $this->csrf() ?>">
            <input type="hidden" name="return_url" value="<?= $this->e($returnUrl ?? '/admin') ?>">
            
            <!-- Flash Messages -->
            <?php if ($this->getFlash('error')): ?>
            <div class="alert alert-error">
                <?= $this->e($this->getFlash('error')) ?>
            </div>
            <?php endif; ?>
            
            <?php if ($this->getFlash('success')): ?>
            <div class="alert alert-success">
                <?= $this->e($this->getFlash('success')) ?>
            </div>
            <?php endif; ?>
            
            <!-- Email -->
            <div class="form-group">
                <label for="email" class="form-label">E-posta Adresi</label>
                <input type="email" 
                       id="email" 
                       name="email" 
                       class="form-input" 
                       placeholder="<EMAIL>"
                       value="<?= $this->e($this->old('email')) ?>"
                       required 
                       autofocus>
            </div>
            
            <!-- Password -->
            <div class="form-group">
                <label for="password" class="form-label">Şifre</label>
                <input type="password" 
                       id="password" 
                       name="password" 
                       class="form-input" 
                       placeholder="••••••••"
                       required>
            </div>
            
            <!-- Remember Me -->
            <div class="form-checkbox">
                <input type="checkbox" id="remember" name="remember" value="1">
                <label for="remember">Beni hatırla</label>
            </div>
            
            <!-- Submit -->
            <button type="submit" class="login-button">
                Giriş Yap
            </button>
        </form>
        
        <!-- Footer -->
        <div class="login-footer">
            <a href="/" class="back-link">← Ana Sayfaya Dön</a>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="<?= $this->asset('js/app.js') ?>"></script>
</body>
</html>
