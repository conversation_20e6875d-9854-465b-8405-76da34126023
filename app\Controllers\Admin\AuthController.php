<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;

class AuthController extends BaseController
{
    public function loginForm(): void
    {
        // Zaten giriş yapmışsa dashboard'a yönlendir
        if ($this->isLoggedIn() && $this->isAdmin()) {
            $this->redirect('/admin');
            return;
        }

        $data = [
            'title' => 'Admin Girişi',
            'returnUrl' => $this->getInput('return', '/admin'),
        ];

        $this->view('admin/pages/login', $data);
    }

    public function login(): void
    {
        if (!$this->isPost()) {
            $this->redirect('/admin/login');
            return;
        }

        // CSRF kontrolü
        if (!$this->verifyCsrf()) {
            $this->flash('error', 'Güvenlik hatası. Lütfen tekrar deneyin.');
            $this->back();
            return;
        }

        $email = $this->getInput('email');
        $password = $this->getInput('password');
        $remember = (bool) $this->getInput('remember');
        $returnUrl = $this->getInput('return_url', '/admin');

        // Validation
        if (empty($email) || empty($password)) {
            $this->flash('error', 'E-posta ve şifre alanları zorunludur.');
            $this->back();
            return;
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->flash('error', 'Geçerli bir e-posta adresi girin.');
            $this->back();
            return;
        }

        try {
            $auth = $this->auth();
            
            if ($auth->attempt($email, $password, $remember)) {
                // Admin kontrolü
                if (!$auth->isAdmin()) {
                    $auth->logout();
                    $this->flash('error', 'Bu alana erişim yetkiniz bulunmamaktadır.');
                    $this->back();
                    return;
                }

                $this->flash('success', 'Başarıyla giriş yaptınız.');
                $this->redirect($returnUrl);
            } else {
                $this->flash('error', 'E-posta veya şifre hatalı.');
                $this->back();
            }
        } catch (\Exception $e) {
            error_log("Admin login error: " . $e->getMessage());
            $this->flash('error', 'Giriş yapılırken bir hata oluştu.');
            $this->back();
        }
    }

    public function logout(): void
    {
        if (!$this->isPost()) {
            $this->redirect('/admin');
            return;
        }

        // CSRF kontrolü
        if (!$this->verifyCsrf()) {
            $this->redirect('/admin');
            return;
        }

        try {
            $this->auth()->logout();
            $this->flash('success', 'Başarıyla çıkış yaptınız.');
            $this->redirect('/admin/login');
        } catch (\Exception $e) {
            error_log("Admin logout error: " . $e->getMessage());
            $this->redirect('/admin');
        }
    }
}
