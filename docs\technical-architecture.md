# EticSimple E-Ticaret Sistemi - Teknik Mimari

## 🏗️ Sistem Mimarisi Genel Bakış

EticSimple, geleneksel monolitik MVC mimarisine dayalı, modern web standartlarına uygun ve Türkiye pazarına özel tasarlanmış bir e-ticaret sistemidir.

### Temel Mimari Prensipleri
- **Monolitik MVC Yaklaşım**: Frontend ve backend tek uygulamada birleşik
- **Modüler Sınıf <PERSON>**: Bağımsız geliştirilebilir modüller
- **Template Engine**: Blade benzeri template sistemi
- **Security by Design**: <PERSON><PERSON><PERSON><PERSON> her katmanda öncelik
- **Performance Optimized**: Yüksek performans odaklı

---

## 🛠️ Teknoloji Stack

### Backend & Frontend (Monolitik)
- **PHP 8.3.16** (Mevcut stabil sürüm)
- **MySQL 8.0+** (Veritabanı)
- **Redis** (Cache ve Session)
- **Nginx/Apache** (Web Server)
- **Composer** (Dependency Management)

### Template & UI
- **Custom Template Engine** (Blade benzeri)
- **HTML5** (Semantic markup)
- **CSS3** (Modern CSS features)
- **JavaScript ES2024+** (Modern JS)
- **CSS Custom Properties** (Tema sistemi)

### DevOps & Tools
- **Git** (Version control)
- **Docker** (Containerization)
- **PHPUnit** (Testing)
- **Webpack/Vite** (Build tools)
- **ESLint/Prettier** (Code quality)

---

## 🏛️ Monolitik Sistem Mimarisi Diyagramı

```
┌─────────────────────────────────────────────────────────────┐
│                 ETICSIMPLE MONOLITIK UYGULAMA              │
├─────────────────────────────────────────────────────────────┤
│                    PRESENTATION LAYER                       │
│  ┌─────────────────────┐  ┌─────────────────────────────┐   │
│  │   Mağaza Views      │  │      Admin Views            │   │
│  │   (/* routes)       │  │      (/admin/* routes)      │   │
│  │  - Ana Sayfa        │  │  - Dashboard                │   │
│  │  - Ürün Katalogu   │  │  - Ürün Yönetimi           │   │
│  │  - Sepet & Ödeme   │  │  - Sipariş Yönetimi         │   │
│  │  - Kullanıcı Hesabı│  │  - Müşteri Yönetimi         │   │
│  └─────────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    CONTROLLER LAYER                         │
├─────────────────────────────────────────────────────────────┤
│  Store Controllers       │  Admin Controllers               │
│  - HomeController        │  - AdminDashboardController     │
│  - ProductController     │  - AdminProductController       │
│  - CartController        │  - AdminOrderController         │
│  - CheckoutController    │  - AdminUserController          │
│  - UserController        │  - AdminSettingsController      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     SERVICE LAYER                           │
├─────────────────────────────────────────────────────────────┤
│  Business Services       │  Core Services                   │
│  - ProductService        │  - AuthService                  │
│  - OrderService          │  - SessionService               │
│  - PaymentService        │  - CacheService                 │
│  - ShippingService       │  - ValidationService           │
│  - ThemeService          │  - EmailService                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     MODEL LAYER                             │
├─────────────────────────────────────────────────────────────┤
│  Data Models             │  Repository Pattern              │
│  - User                  │  - UserRepository               │
│  - Product               │  - ProductRepository            │
│  - Order                 │  - OrderRepository              │
│  - Category              │  - CategoryRepository           │
│  - Theme                 │  - ThemeRepository              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    DATA LAYER                               │
├─────────────────────────────────────────────────────────────┤
│  MySQL Database          │  Redis Cache                    │
│  - Transactional Data    │  - Session Storage              │
│  - User Data             │  - Cache Data                   │
│  - Product Data          │  - Temporary Data               │
│  - Order Data            │  - Theme Data                   │
└─────────────────────────────────────────────────────────────┘
```

---

## 📁 Monolitik Proje Klasör Yapısı

```
eticsimple/
├── app/                          # Ana uygulama
│   ├── Controllers/              # MVC Controllers
│   │   ├── Store/                # Mağaza controllers
│   │   │   ├── HomeController.php
│   │   │   ├── ProductController.php
│   │   │   ├── CartController.php
│   │   │   └── CheckoutController.php
│   │   └── Admin/                # Admin controllers
│   │       ├── DashboardController.php
│   │       ├── ProductController.php
│   │       ├── OrderController.php
│   │       └── UserController.php
│   ├── Models/                   # Data Models
│   │   ├── User.php
│   │   ├── Product.php
│   │   ├── Order.php
│   │   ├── Category.php
│   │   └── Theme.php
│   ├── Services/                 # Business Logic
│   │   ├── AuthService.php
│   │   ├── ProductService.php
│   │   ├── OrderService.php
│   │   ├── PaymentService.php
│   │   └── ThemeService.php
│   ├── Repositories/             # Data Access Layer
│   │   ├── UserRepository.php
│   │   ├── ProductRepository.php
│   │   └── OrderRepository.php
│   └── Middleware/               # Request Middleware
│       ├── AuthMiddleware.php
│       ├── AdminMiddleware.php
│       └── CSRFMiddleware.php
├── core/                         # Framework Core
│   ├── Database/                 # Database Layer
│   ├── Router/                   # Routing System
│   ├── Template/                 # Template Engine
│   ├── Auth/                     # Authentication
│   ├── Cache/                    # Cache System
│   ├── Session/                  # Session Management
│   └── Validation/               # Input Validation
├── views/                        # Template Files
│   ├── store/                    # Mağaza templates
│   │   ├── layouts/              # Layout templates
│   │   ├── pages/                # Page templates
│   │   └── components/           # Reusable components
│   └── admin/                    # Admin templates
│       ├── layouts/              # Admin layouts
│       ├── pages/                # Admin pages
│       └── components/           # Admin components
├── public/                       # Web Root
│   ├── index.php                 # Entry point
│   ├── assets/                   # Static assets
│   │   ├── css/                  # Stylesheets
│   │   ├── js/                   # JavaScript
│   │   └── images/               # Images
│   └── uploads/                  # User uploads
├── config/                       # Configuration
│   ├── database.php
│   ├── app.php
│   └── themes.php
├── migrations/                   # Database Migrations
├── seeds/                        # Database Seeds
├── tests/                        # Unit Tests
├── docs/                         # Documentation
└── vendor/                       # Composer Dependencies
```

---

## 🗄️ Veritabanı Şeması (Ana Tablolar)

### Kullanıcı Yönetimi
```sql
-- Kullanıcılar
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    tc_kimlik_no VARCHAR(11) UNIQUE,
    birth_date DATE,
    gender ENUM('male', 'female', 'other'),
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_tc_kimlik (tc_kimlik_no),
    INDEX idx_status (status)
);

-- Kullanıcı Adresleri
CREATE TABLE user_addresses (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(100) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    company VARCHAR(255),
    address_line_1 TEXT NOT NULL,
    address_line_2 TEXT,
    city VARCHAR(100) NOT NULL,
    district VARCHAR(100) NOT NULL,
    postal_code VARCHAR(10) NOT NULL,
    country VARCHAR(2) DEFAULT 'TR',
    phone VARCHAR(20),
    is_default BOOLEAN DEFAULT FALSE,
    type ENUM('billing', 'shipping', 'both') DEFAULT 'both',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_default (is_default)
);
```

### Ürün Yönetimi
```sql
-- Kategoriler
CREATE TABLE categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    parent_id BIGINT UNSIGNED NULL,
    name JSON NOT NULL, -- Çoklu dil desteği
    slug VARCHAR(255) UNIQUE NOT NULL,
    description JSON,
    image VARCHAR(500),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    meta_title JSON,
    meta_description JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_parent (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_active (is_active)
);

-- Ürünler
CREATE TABLE products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    sku VARCHAR(100) UNIQUE NOT NULL,
    name JSON NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description JSON,
    short_description JSON,
    price DECIMAL(10,2) NOT NULL,
    compare_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    track_inventory BOOLEAN DEFAULT TRUE,
    inventory_quantity INT DEFAULT 0,
    weight DECIMAL(8,2),
    dimensions JSON, -- {length, width, height}
    images JSON, -- Array of image URLs
    status ENUM('active', 'draft', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    meta_title JSON,
    meta_description JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_sku (sku),
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_price (price)
);

-- Ürün-Kategori İlişkisi
CREATE TABLE product_categories (
    product_id BIGINT UNSIGNED NOT NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    
    PRIMARY KEY (product_id, category_id),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);
```

### Sipariş Yönetimi
```sql
-- Siparişler
CREATE TABLE orders (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT UNSIGNED,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    shipping_method VARCHAR(50),
    currency VARCHAR(3) DEFAULT 'TRY',
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    billing_address JSON NOT NULL,
    shipping_address JSON NOT NULL,
    notes TEXT,
    tracking_number VARCHAR(100),
    shipped_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_order_number (order_number),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
);

-- Sipariş Kalemleri
CREATE TABLE order_items (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT UNSIGNED NOT NULL,
    product_id BIGINT UNSIGNED NOT NULL,
    product_sku VARCHAR(100) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    product_data JSON, -- Snapshot of product at order time
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
);
```

### Tema Yönetimi
```sql
-- Temalar
CREATE TABLE themes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    version VARCHAR(20) NOT NULL,
    author VARCHAR(100),
    preview_image VARCHAR(500),
    css_variables JSON NOT NULL, -- Tema CSS değişkenleri
    is_active BOOLEAN DEFAULT FALSE,
    is_default BOOLEAN DEFAULT FALSE,
    settings JSON, -- Tema özel ayarları
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_slug (slug),
    INDEX idx_active (is_active),
    INDEX idx_default (is_default)
);

-- Site Ayarları
CREATE TABLE site_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value JSON,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'file') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE, -- Frontend'de erişilebilir mi
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_key (setting_key),
    INDEX idx_category (category),
    INDEX idx_public (is_public)
);

-- Tema Ayarları (Kullanıcı özelleştirmeleri)
CREATE TABLE theme_customizations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    theme_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL, -- NULL ise site geneli
    customization_key VARCHAR(100) NOT NULL,
    customization_value JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (theme_id) REFERENCES themes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_theme_user_key (theme_id, user_id, customization_key),
    INDEX idx_theme_id (theme_id),
    INDEX idx_user_id (user_id)
);
```

---

## 🔐 Güvenlik Mimarisi

### Authentication & Authorization
- **Session-based** authentication (PHP sessions)
- **Role-based Access Control** (RBAC)
- **Admin/User** role separation
- **Rate Limiting** per user/IP
- **CSRF Protection** all forms
- **XSS Protection** input sanitization

### Data Security
- **Password Hashing**: Argon2ID algorithm
- **Data Encryption**: AES-256 for sensitive data
- **SSL/TLS**: HTTPS zorunlu
- **Database Encryption**: Sensitive fields encrypted
- **Audit Logging**: All critical operations logged
- **Input Validation**: Comprehensive validation rules

---

## 🚀 Performance Optimizations

### Caching Strategy
- **Redis**: Session, user data, frequently accessed data
- **Database Query Cache**: MySQL query result cache
- **HTTP Cache**: Browser and CDN caching
- **Object Cache**: Application level caching

### Database Optimizations
- **Indexing Strategy**: Optimized indexes for queries
- **Query Optimization**: Efficient SQL queries
- **Connection Pooling**: Database connection management
- **Read Replicas**: Read operations scaling

---

## 🔌 Routing Tasarımı

### Mağaza Routes (Store)
```
GET    /                          # Ana sayfa
GET    /products                  # Ürün listesi
GET    /products/{slug}           # Ürün detayı
GET    /category/{slug}           # Kategori sayfası
GET    /search                    # Arama sonuçları
GET    /cart                      # Sepet sayfası
POST   /cart/add                  # Sepete ekleme
POST   /cart/update               # Sepet güncelleme
POST   /cart/remove               # Sepetten çıkarma
GET    /checkout                  # Ödeme sayfası
POST   /checkout/process          # Sipariş işleme
GET    /account                   # Kullanıcı hesabı
GET    /account/orders            # Sipariş geçmişi
GET    /account/addresses         # Adres defteri
POST   /account/addresses         # Adres ekleme
```

### Admin Routes (Admin Panel)
```
GET    /admin                     # Admin dashboard
GET    /admin/login               # Admin giriş
POST   /admin/login               # Admin giriş işleme
GET    /admin/products            # Ürün yönetimi
GET    /admin/products/create     # Ürün ekleme
POST   /admin/products            # Ürün kaydetme
GET    /admin/products/{id}/edit  # Ürün düzenleme
PUT    /admin/products/{id}       # Ürün güncelleme
DELETE /admin/products/{id}       # Ürün silme
GET    /admin/orders              # Sipariş yönetimi
GET    /admin/orders/{id}         # Sipariş detayı
PUT    /admin/orders/{id}/status  # Sipariş durumu güncelleme
GET    /admin/users               # Kullanıcı yönetimi
GET    /admin/themes              # Tema yönetimi
POST   /admin/themes/activate     # Tema aktifleştirme
GET    /admin/settings            # Site ayarları
POST   /admin/settings            # Ayar güncelleme
```

### Authentication Routes
```
GET    /login                     # Giriş sayfası
POST   /login                     # Giriş işleme
GET    /register                  # Kayıt sayfası
POST   /register                  # Kayıt işleme
POST   /logout                    # Çıkış
GET    /forgot-password           # Şifre sıfırlama
POST   /forgot-password           # Şifre sıfırlama işleme
GET    /reset-password/{token}    # Şifre yenileme
POST   /reset-password            # Şifre yenileme işleme
```

---

## 🌍 Çoklu Dil Desteği Mimarisi

### Dil Dosyası Yapısı
```
/languages/
├── tr/                    # Türkçe
│   ├── common.json
│   ├── products.json
│   ├── checkout.json
│   └── errors.json
├── en/                    # İngilizce
│   ├── common.json
│   ├── products.json
│   ├── checkout.json
│   └── errors.json
└── de/                    # Almanca (gelecek)
```

### Veritabanında Çoklu Dil
```sql
-- JSON formatında çoklu dil desteği
{
  "tr": "Ürün Adı",
  "en": "Product Name",
  "de": "Produktname"
}
```

---

## 📊 Monitoring ve Logging

### Application Logging
```php
// Log seviyeleri
- EMERGENCY: Sistem kullanılamaz
- ALERT: Acil müdahale gerekli
- CRITICAL: Kritik durumlar
- ERROR: Hata durumları
- WARNING: Uyarı durumları
- NOTICE: Normal ama önemli olaylar
- INFO: Bilgilendirme mesajları
- DEBUG: Debug bilgileri
```

### Performance Monitoring
- **Response Time**: API response süreleri
- **Database Queries**: Sorgu performansı
- **Memory Usage**: Bellek kullanımı
- **CPU Usage**: İşlemci kullanımı
- **Error Rates**: Hata oranları

---

## 🔄 Deployment Mimarisi

### Environment Yapısı
```
Development → Staging → Production
     ↓           ↓         ↓
   Local      Test      Live
```

### Docker Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  web:
    build: ./docker/nginx
    ports:
      - "80:80"
      - "443:443"

  php:
    build: ./docker/php
    volumes:
      - ./api:/var/www/html

  mysql:
    image: mysql:8.4
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

---

## 🧪 Testing Strategy

### Test Türleri
- **Unit Tests**: Birim testler (PHPUnit)
- **Integration Tests**: Entegrasyon testleri
- **API Tests**: API endpoint testleri
- **E2E Tests**: End-to-end testler
- **Performance Tests**: Performans testleri

### Test Coverage Hedefi
- **Minimum %80** kod coverage
- **%100** critical path coverage
- **Automated testing** CI/CD pipeline

---

*Bu teknik mimari dökümanı proje gelişimi sürecinde detaylandırılacaktır.*
