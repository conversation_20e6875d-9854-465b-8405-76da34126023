# EticSimple E-Ticaret Sistemi - Teknik Mimari

## 🏗️ Sistem Mimarisi Genel Bakış

EticSimple, modern web standartlarına uygun, modüler ve ölçeklenebilir bir e-ticaret sistemi olarak tasarlanmıştır.

### Temel Mimari Prensipleri
- **API-First Yaklaşım**: Frontend ve backend tamamen ayrı
- **Modüler Yapı**: Bağımsız geliştirilebilir modüller
- **Microservices Ready**: Gelecekte microservices'e geçiş hazırlığı
- **Security by Design**: <PERSON><PERSON><PERSON><PERSON> her katmanda öncelik
- **Performance Optimized**: Yüksek performans odaklı

---

## 🛠️ Teknoloji Stack

### Backend
- **PHP 8.4+** (En güncel LTS sürüm)
- **MySQL 8.4+** (Veritabanı)
- **Redis** (Cache ve Session)
- **Nginx** (Web Server)
- **Composer** (Dependency Management)

### Frontend
- **HTML5** (Semantic markup)
- **CSS3** (Modern CSS features)
- **JavaScript ES2024+** (Modern JS)
- **Web Components** (Reusable components)
- **Service Workers** (PWA support)

### DevOps & Tools
- **Git** (Version control)
- **Docker** (Containerization)
- **PHPUnit** (Testing)
- **Webpack/Vite** (Build tools)
- **ESLint/Prettier** (Code quality)

---

## 🏛️ Sistem Mimarisi Diyagramı

```
┌─────────────────────────────────────────────────────────────┐
│                    FRONTEND LAYER                           │
├─────────────────────────────────────────────────────────────┤
│  Mağaza (Store)          │         Admin Panel              │
│  - Ürün Katalogu        │         - Ürün Yönetimi         │
│  - Sepet & Ödeme        │         - Sipariş Yönetimi      │
│  - Kullanıcı Hesabı     │         - Müşteri Yönetimi      │
│  - Arama & Filtreleme   │         - Raporlar               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     API GATEWAY                             │
├─────────────────────────────────────────────────────────────┤
│  - Authentication        │  - Rate Limiting                │
│  - Authorization         │  - Request Validation           │
│  - Routing               │  - Response Formatting          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   BACKEND SERVICES                          │
├─────────────────────────────────────────────────────────────┤
│  Core Services           │  Business Services               │
│  - User Management       │  - Product Catalog              │
│  - Authentication        │  - Order Management             │
│  - Authorization         │  - Payment Processing           │
│  - Session Management    │  - Shipping Management          │
│                          │  - Marketing & Campaigns        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    DATA LAYER                               │
├─────────────────────────────────────────────────────────────┤
│  MySQL Database          │  Redis Cache                    │
│  - Transactional Data    │  - Session Storage              │
│  - User Data             │  - Cache Data                   │
│  - Product Data          │  - Temporary Data               │
│  - Order Data            │                                 │
└─────────────────────────────────────────────────────────────┘
```

---

## 📁 Proje Klasör Yapısı

```
eticsimple/
├── api/                          # Backend API
│   ├── config/                   # Konfigürasyon dosyaları
│   ├── core/                     # Core framework
│   │   ├── Database/             # Veritabanı katmanı
│   │   ├── Router/               # Routing sistemi
│   │   ├── Auth/                 # Authentication
│   │   ├── Cache/                # Cache sistemi
│   │   └── Validation/           # Validation
│   ├── modules/                  # İş modülleri
│   │   ├── User/                 # Kullanıcı yönetimi
│   │   ├── Product/              # Ürün yönetimi
│   │   ├── Order/                # Sipariş yönetimi
│   │   ├── Payment/              # Ödeme sistemi
│   │   ├── Shipping/             # Kargo sistemi
│   │   └── Marketing/            # Pazarlama
│   ├── middleware/               # Middleware'ler
│   ├── migrations/               # Veritabanı migration'ları
│   ├── tests/                    # Unit testler
│   └── public/                   # Public API endpoint
├── store/                        # Frontend Mağaza
│   ├── assets/                   # CSS, JS, Images
│   ├── components/               # Reusable components
│   ├── pages/                    # Sayfa templates
│   ├── js/                       # JavaScript modules
│   └── css/                      # Stylesheet'ler
├── admin/                        # Admin Panel
│   ├── assets/                   # Admin assets
│   ├── components/               # Admin components
│   ├── pages/                    # Admin pages
│   └── js/                       # Admin JavaScript
├── docs/                         # Dokümantasyon
├── docker/                       # Docker konfigürasyonları
└── scripts/                      # Build ve deployment scriptleri
```

---

## 🗄️ Veritabanı Şeması (Ana Tablolar)

### Kullanıcı Yönetimi
```sql
-- Kullanıcılar
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    tc_kimlik_no VARCHAR(11) UNIQUE,
    birth_date DATE,
    gender ENUM('male', 'female', 'other'),
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_tc_kimlik (tc_kimlik_no),
    INDEX idx_status (status)
);

-- Kullanıcı Adresleri
CREATE TABLE user_addresses (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(100) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    company VARCHAR(255),
    address_line_1 TEXT NOT NULL,
    address_line_2 TEXT,
    city VARCHAR(100) NOT NULL,
    district VARCHAR(100) NOT NULL,
    postal_code VARCHAR(10) NOT NULL,
    country VARCHAR(2) DEFAULT 'TR',
    phone VARCHAR(20),
    is_default BOOLEAN DEFAULT FALSE,
    type ENUM('billing', 'shipping', 'both') DEFAULT 'both',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_default (is_default)
);
```

### Ürün Yönetimi
```sql
-- Kategoriler
CREATE TABLE categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    parent_id BIGINT UNSIGNED NULL,
    name JSON NOT NULL, -- Çoklu dil desteği
    slug VARCHAR(255) UNIQUE NOT NULL,
    description JSON,
    image VARCHAR(500),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    meta_title JSON,
    meta_description JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_parent (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_active (is_active)
);

-- Ürünler
CREATE TABLE products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    sku VARCHAR(100) UNIQUE NOT NULL,
    name JSON NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description JSON,
    short_description JSON,
    price DECIMAL(10,2) NOT NULL,
    compare_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    track_inventory BOOLEAN DEFAULT TRUE,
    inventory_quantity INT DEFAULT 0,
    weight DECIMAL(8,2),
    dimensions JSON, -- {length, width, height}
    images JSON, -- Array of image URLs
    status ENUM('active', 'draft', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    meta_title JSON,
    meta_description JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_sku (sku),
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_price (price)
);

-- Ürün-Kategori İlişkisi
CREATE TABLE product_categories (
    product_id BIGINT UNSIGNED NOT NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    
    PRIMARY KEY (product_id, category_id),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);
```

### Sipariş Yönetimi
```sql
-- Siparişler
CREATE TABLE orders (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT UNSIGNED,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    shipping_method VARCHAR(50),
    currency VARCHAR(3) DEFAULT 'TRY',
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    billing_address JSON NOT NULL,
    shipping_address JSON NOT NULL,
    notes TEXT,
    tracking_number VARCHAR(100),
    shipped_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_order_number (order_number),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
);

-- Sipariş Kalemleri
CREATE TABLE order_items (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT UNSIGNED NOT NULL,
    product_id BIGINT UNSIGNED NOT NULL,
    product_sku VARCHAR(100) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    product_data JSON, -- Snapshot of product at order time
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
);
```

### Tema Yönetimi
```sql
-- Temalar
CREATE TABLE themes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    version VARCHAR(20) NOT NULL,
    author VARCHAR(100),
    preview_image VARCHAR(500),
    css_variables JSON NOT NULL, -- Tema CSS değişkenleri
    is_active BOOLEAN DEFAULT FALSE,
    is_default BOOLEAN DEFAULT FALSE,
    settings JSON, -- Tema özel ayarları
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_slug (slug),
    INDEX idx_active (is_active),
    INDEX idx_default (is_default)
);

-- Site Ayarları
CREATE TABLE site_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value JSON,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'file') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE, -- Frontend'de erişilebilir mi
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_key (setting_key),
    INDEX idx_category (category),
    INDEX idx_public (is_public)
);

-- Tema Ayarları (Kullanıcı özelleştirmeleri)
CREATE TABLE theme_customizations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    theme_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL, -- NULL ise site geneli
    customization_key VARCHAR(100) NOT NULL,
    customization_value JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (theme_id) REFERENCES themes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_theme_user_key (theme_id, user_id, customization_key),
    INDEX idx_theme_id (theme_id),
    INDEX idx_user_id (user_id)
);
```

---

## 🔐 Güvenlik Mimarisi

### Authentication & Authorization
- **JWT Token** tabanlı authentication
- **Role-based Access Control** (RBAC)
- **API Key** authentication for external integrations
- **Rate Limiting** per user/IP
- **CSRF Protection** all forms
- **XSS Protection** input sanitization

### Data Security
- **Password Hashing**: Argon2ID algorithm
- **Data Encryption**: AES-256 for sensitive data
- **SSL/TLS**: HTTPS zorunlu
- **Database Encryption**: Sensitive fields encrypted
- **Audit Logging**: All critical operations logged

---

## 🚀 Performance Optimizations

### Caching Strategy
- **Redis**: Session, user data, frequently accessed data
- **Database Query Cache**: MySQL query result cache
- **HTTP Cache**: Browser and CDN caching
- **Object Cache**: Application level caching

### Database Optimizations
- **Indexing Strategy**: Optimized indexes for queries
- **Query Optimization**: Efficient SQL queries
- **Connection Pooling**: Database connection management
- **Read Replicas**: Read operations scaling

---

## 🔌 API Tasarımı

### RESTful API Endpoints

#### Authentication
```
POST   /api/auth/login
POST   /api/auth/register
POST   /api/auth/logout
POST   /api/auth/refresh
POST   /api/auth/forgot-password
POST   /api/auth/reset-password
```

#### Products
```
GET    /api/products              # Ürün listesi
GET    /api/products/{id}         # Ürün detayı
GET    /api/products/search       # Ürün arama
GET    /api/categories            # Kategori listesi
GET    /api/categories/{id}/products # Kategoriye göre ürünler
```

#### Cart & Orders
```
GET    /api/cart                  # Sepet görüntüleme
POST   /api/cart/add              # Sepete ekleme
PUT    /api/cart/update           # Sepet güncelleme
DELETE /api/cart/remove           # Sepetten çıkarma
POST   /api/orders                # Sipariş oluşturma
GET    /api/orders                # Sipariş listesi
GET    /api/orders/{id}           # Sipariş detayı
```

#### User Management
```
GET    /api/user/profile          # Profil bilgileri
PUT    /api/user/profile          # Profil güncelleme
GET    /api/user/addresses        # Adres listesi
POST   /api/user/addresses        # Adres ekleme
PUT    /api/user/addresses/{id}   # Adres güncelleme
DELETE /api/user/addresses/{id}   # Adres silme
```

#### Theme Management
```
GET    /api/themes                # Tema listesi
GET    /api/themes/{id}           # Tema detayı
POST   /api/themes/activate       # Tema aktifleştirme
GET    /api/themes/current        # Aktif tema bilgileri
PUT    /api/themes/customize      # Tema özelleştirme
GET    /api/settings              # Site ayarları (public)

# Admin endpoints
GET    /api/admin/themes          # Tüm temalar (admin)
POST   /api/admin/themes          # Tema ekleme
PUT    /api/admin/themes/{id}     # Tema güncelleme
DELETE /api/admin/themes/{id}     # Tema silme
GET    /api/admin/settings        # Tüm site ayarları
PUT    /api/admin/settings        # Site ayarları güncelleme
```

### API Response Format
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "İşlem başarılı",
  "errors": [],
  "meta": {
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "per_page": 20,
      "total_items": 200
    }
  }
}
```

---

## 🌍 Çoklu Dil Desteği Mimarisi

### Dil Dosyası Yapısı
```
/languages/
├── tr/                    # Türkçe
│   ├── common.json
│   ├── products.json
│   ├── checkout.json
│   └── errors.json
├── en/                    # İngilizce
│   ├── common.json
│   ├── products.json
│   ├── checkout.json
│   └── errors.json
└── de/                    # Almanca (gelecek)
```

### Veritabanında Çoklu Dil
```sql
-- JSON formatında çoklu dil desteği
{
  "tr": "Ürün Adı",
  "en": "Product Name",
  "de": "Produktname"
}
```

---

## 📊 Monitoring ve Logging

### Application Logging
```php
// Log seviyeleri
- EMERGENCY: Sistem kullanılamaz
- ALERT: Acil müdahale gerekli
- CRITICAL: Kritik durumlar
- ERROR: Hata durumları
- WARNING: Uyarı durumları
- NOTICE: Normal ama önemli olaylar
- INFO: Bilgilendirme mesajları
- DEBUG: Debug bilgileri
```

### Performance Monitoring
- **Response Time**: API response süreleri
- **Database Queries**: Sorgu performansı
- **Memory Usage**: Bellek kullanımı
- **CPU Usage**: İşlemci kullanımı
- **Error Rates**: Hata oranları

---

## 🔄 Deployment Mimarisi

### Environment Yapısı
```
Development → Staging → Production
     ↓           ↓         ↓
   Local      Test      Live
```

### Docker Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  web:
    build: ./docker/nginx
    ports:
      - "80:80"
      - "443:443"

  php:
    build: ./docker/php
    volumes:
      - ./api:/var/www/html

  mysql:
    image: mysql:8.4
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

---

## 🧪 Testing Strategy

### Test Türleri
- **Unit Tests**: Birim testler (PHPUnit)
- **Integration Tests**: Entegrasyon testleri
- **API Tests**: API endpoint testleri
- **E2E Tests**: End-to-end testler
- **Performance Tests**: Performans testleri

### Test Coverage Hedefi
- **Minimum %80** kod coverage
- **%100** critical path coverage
- **Automated testing** CI/CD pipeline

---

*Bu teknik mimari dökümanı proje gelişimi sürecinde detaylandırılacaktır.*
