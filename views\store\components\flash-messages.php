<?php
// Flash messages component
$app = \Core\Application::getInstance();
$session = $app->getSessionManager();
$flashMessages = $session->getAllFlash();

if (empty($flashMessages)) {
    return;
}
?>

<div class="flash-messages">
    <?php foreach ($flashMessages as $type => $message): ?>
    <div class="flash-message flash-<?= $this->e($type) ?>" data-flash-message>
        <div class="flash-content">
            <div class="flash-icon">
                <?php if ($type === 'success'): ?>
                    <i class="icon-check-circle"></i>
                <?php elseif ($type === 'error'): ?>
                    <i class="icon-x-circle"></i>
                <?php elseif ($type === 'warning'): ?>
                    <i class="icon-alert-triangle"></i>
                <?php else: ?>
                    <i class="icon-info"></i>
                <?php endif; ?>
            </div>
            <div class="flash-text">
                <?= $this->e($message) ?>
            </div>
            <button class="flash-close" data-flash-close>
                <i class="icon-x"></i>
            </button>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<style>
/* Flash Messages Styles */
.flash-messages {
    position: fixed;
    top: var(--space-4);
    right: var(--space-4);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    max-width: 400px;
    width: 100%;
}

.flash-message {
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    animation: slideInRight 0.3s ease-out;
}

.flash-content {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    padding: var(--space-4);
}

.flash-icon {
    flex-shrink: 0;
    font-size: 1.25rem;
    margin-top: 2px;
}

.flash-text {
    flex: 1;
    font-weight: 500;
    line-height: 1.4;
}

.flash-close {
    flex-shrink: 0;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.flash-close:hover {
    opacity: 1;
}

/* Flash Message Types */
.flash-success {
    background-color: var(--theme-success);
    color: white;
}

.flash-error {
    background-color: var(--theme-error);
    color: white;
}

.flash-warning {
    background-color: var(--theme-warning);
    color: white;
}

.flash-info {
    background-color: var(--theme-info);
    color: white;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.flash-message.removing {
    animation: slideOutRight 0.3s ease-out forwards;
}

/* Responsive */
@media (max-width: 768px) {
    .flash-messages {
        top: var(--space-2);
        right: var(--space-2);
        left: var(--space-2);
        max-width: none;
    }
    
    .flash-content {
        padding: var(--space-3);
    }
}
</style>

<script>
// Flash message auto-hide and close functionality
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[data-flash-message]');
    
    flashMessages.forEach(function(message) {
        // Auto-hide after 5 seconds
        setTimeout(function() {
            hideFlashMessage(message);
        }, 5000);
        
        // Close button functionality
        const closeBtn = message.querySelector('[data-flash-close]');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                hideFlashMessage(message);
            });
        }
    });
    
    function hideFlashMessage(message) {
        message.classList.add('removing');
        setTimeout(function() {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 300);
    }
});
</script>
