<?php

namespace App\Controllers\Admin;

use App\Models\Product;

class ProductController extends AdminController
{
    /**
     * Ürün listesi
     */
    public function index(): void
    {
        try {
            $page = max(1, (int) $this->getInput('page', 1));
            $limit = 20;
            $offset = ($page - 1) * $limit;

            // Filtreler
            $filters = [
                'search' => $this->getInput('search'),
                'category_id' => $this->getInput('category'),
                'status' => $this->getInput('status'),
                'featured' => $this->getInput('featured'),
                'low_stock' => $this->getInput('low_stock')
            ];

            // Ürünleri getir (admin flag ekle)
            $filters['admin'] = true;
            $products = $this->productRepository->getAll($limit, $offset, ['filters' => $filters]);
            $totalCount = $this->productRepository->countAll($filters);
            
            // Kategorileri getir (filtre için)
            $categories = $this->categoryRepository->getAll();

            $pagination = $this->paginate($totalCount, $page, $limit);

            $data = [
                'title' => 'Ürün Yönetimi',
                'products' => $products,
                'categories' => $categories,
                'filters' => $filters,
                'pagination' => $pagination,
                'user' => $this->user(),
            ];

            $this->view('admin/pages/products/index', $data);

        } catch (\Exception $e) {
            error_log("Admin products index error: " . $e->getMessage());
            
            $data = [
                'title' => 'Ürün Yönetimi',
                'products' => [],
                'categories' => [],
                'filters' => [],
                'pagination' => $this->paginate(0, 1, 20),
                'user' => $this->user(),
                'error' => 'Ürünler yüklenirken bir hata oluştu.'
            ];

            $this->view('admin/pages/products/index', $data);
        }
    }

    /**
     * Yeni ürün oluşturma formu
     */
    public function create(): void
    {
        try {
            $categories = $this->categoryRepository->getAll();

            $data = [
                'title' => 'Yeni Ürün Ekle',
                'categories' => $categories,
                'user' => $this->user(),
            ];

            $this->view('admin/pages/products/create', $data);

        } catch (\Exception $e) {
            error_log("Admin product create form error: " . $e->getMessage());
            $this->adminFlash('Sayfa yüklenirken bir hata oluştu.', 'error');
            $this->adminRedirect('/products');
        }
    }

    /**
     * Yeni ürün kaydetme
     */
    public function store(): void
    {
        if (!$this->isPost()) {
            $this->adminRedirect('/products/create');
            return;
        }

        // CSRF kontrolü
        if (!$this->verifyCsrf()) {
            $this->adminFlash('Güvenlik hatası. Lütfen tekrar deneyin.', 'error');
            $this->back();
            return;
        }

        $productData = [
            'name' => $this->getInput('name'),
            'slug' => $this->getInput('slug') ?: $this->generateSlug($this->getInput('name')),
            'description' => $this->getInput('description'),
            'short_description' => $this->getInput('short_description'),
            'price' => (float) $this->getInput('price'),
            'compare_price' => $this->getInput('compare_price') ? (float) $this->getInput('compare_price') : null,
            'sku' => $this->getInput('sku'),
            'barcode' => $this->getInput('barcode'),
            'track_inventory' => (bool) $this->getInput('track_inventory'),
            'inventory_quantity' => (int) $this->getInput('inventory_quantity', 0),
            'low_stock_threshold' => (int) $this->getInput('low_stock_threshold', 5),
            'weight' => $this->getInput('weight') ? (float) $this->getInput('weight') : null,
            'dimensions' => $this->getInput('dimensions'),
            'requires_shipping' => (bool) $this->getInput('requires_shipping', true),
            'is_digital' => (bool) $this->getInput('is_digital'),
            'status' => $this->getInput('status', 'active'),
            'featured' => (bool) $this->getInput('featured'),
            'meta_title' => $this->getInput('meta_title'),
            'meta_description' => $this->getInput('meta_description'),
            'attributes' => $this->getInput('attributes', [])
        ];

        // Validation
        $errors = $this->validateProductData($productData);
        if (!empty($errors)) {
            foreach ($errors as $error) {
                $this->adminFlash($error, 'error');
            }
            $this->back();
            return;
        }

        try {
            $product = $this->productRepository->createProduct($productData);
            
            if ($product) {
                // Kategorileri kaydet
                $categoryIds = $this->getInput('categories', []);
                if (!empty($categoryIds)) {
                    $this->saveProductCategories($product->id, $categoryIds);
                }

                $this->adminFlash('Ürün başarıyla oluşturuldu.', 'success');
                $this->adminRedirect('/products/' . $product->id . '/edit');
            } else {
                $this->adminFlash('Ürün oluşturulurken bir hata oluştu.', 'error');
                $this->back();
            }

        } catch (\Exception $e) {
            error_log("Admin product store error: " . $e->getMessage());
            $this->adminFlash('Ürün oluşturulurken bir hata oluştu.', 'error');
            $this->back();
        }
    }

    /**
     * Ürün düzenleme formu
     */
    public function edit(int $id): void
    {
        try {
            $product = $this->productRepository->findProductById($id);
            
            if (!$product) {
                $this->adminFlash('Ürün bulunamadı.', 'error');
                $this->adminRedirect('/products');
                return;
            }

            $categories = $this->categoryRepository->getAll();
            $productCategories = $product->getCategories();
            $productCategoryIds = array_column($productCategories, 'id');

            $data = [
                'title' => 'Ürün Düzenle: ' . $product->name,
                'product' => $product,
                'categories' => $categories,
                'product_categories' => $productCategoryIds,
                'user' => $this->user(),
            ];

            $this->view('admin/pages/products/edit', $data);

        } catch (\Exception $e) {
            error_log("Admin product edit error: " . $e->getMessage());
            $this->adminFlash('Ürün yüklenirken bir hata oluştu.', 'error');
            $this->adminRedirect('/products');
        }
    }

    /**
     * Ürün güncelleme
     */
    public function update(int $id): void
    {
        if (!$this->isPost()) {
            $this->adminRedirect('/products/' . $id . '/edit');
            return;
        }

        // CSRF kontrolü
        if (!$this->verifyCsrf()) {
            $this->adminFlash('Güvenlik hatası. Lütfen tekrar deneyin.', 'error');
            $this->back();
            return;
        }

        try {
            $product = $this->productRepository->findProductById($id);
            
            if (!$product) {
                $this->adminFlash('Ürün bulunamadı.', 'error');
                $this->adminRedirect('/products');
                return;
            }

            $productData = [
                'name' => $this->getInput('name'),
                'slug' => $this->getInput('slug') ?: $this->generateSlug($this->getInput('name')),
                'description' => $this->getInput('description'),
                'short_description' => $this->getInput('short_description'),
                'price' => (float) $this->getInput('price'),
                'compare_price' => $this->getInput('compare_price') ? (float) $this->getInput('compare_price') : null,
                'sku' => $this->getInput('sku'),
                'barcode' => $this->getInput('barcode'),
                'track_inventory' => (bool) $this->getInput('track_inventory'),
                'inventory_quantity' => (int) $this->getInput('inventory_quantity', 0),
                'low_stock_threshold' => (int) $this->getInput('low_stock_threshold', 5),
                'weight' => $this->getInput('weight') ? (float) $this->getInput('weight') : null,
                'dimensions' => $this->getInput('dimensions'),
                'requires_shipping' => (bool) $this->getInput('requires_shipping', true),
                'is_digital' => (bool) $this->getInput('is_digital'),
                'status' => $this->getInput('status', 'active'),
                'featured' => (bool) $this->getInput('featured'),
                'meta_title' => $this->getInput('meta_title'),
                'meta_description' => $this->getInput('meta_description'),
                'attributes' => $this->getInput('attributes', [])
            ];

            // Validation
            $errors = $this->validateProductData($productData, $id);
            if (!empty($errors)) {
                foreach ($errors as $error) {
                    $this->adminFlash($error, 'error');
                }
                $this->back();
                return;
            }

            // Ürünü güncelle
            foreach ($productData as $key => $value) {
                $product->$key = $value;
            }

            if ($product->save()) {
                // Kategorileri güncelle
                $categoryIds = $this->getInput('categories', []);
                $this->saveProductCategories($product->id, $categoryIds);

                $this->adminFlash('Ürün başarıyla güncellendi.', 'success');
                $this->adminRedirect('/products/' . $product->id . '/edit');
            } else {
                $this->adminFlash('Ürün güncellenirken bir hata oluştu.', 'error');
                $this->back();
            }

        } catch (\Exception $e) {
            error_log("Admin product update error: " . $e->getMessage());
            $this->adminFlash('Ürün güncellenirken bir hata oluştu.', 'error');
            $this->back();
        }
    }

    /**
     * Ürün silme
     */
    public function destroy(int $id): void
    {
        if (!$this->isPost()) {
            $this->adminRedirect('/products');
            return;
        }

        // CSRF kontrolü
        if (!$this->verifyCsrf()) {
            $this->adminFlash('Güvenlik hatası. Lütfen tekrar deneyin.', 'error');
            $this->adminRedirect('/products');
            return;
        }

        try {
            $product = $this->productRepository->findProductById($id);
            
            if (!$product) {
                $this->adminFlash('Ürün bulunamadı.', 'error');
                $this->adminRedirect('/products');
                return;
            }

            if ($product->delete()) {
                $this->adminFlash('Ürün başarıyla silindi.', 'success');
            } else {
                $this->adminFlash('Ürün silinirken bir hata oluştu.', 'error');
            }

        } catch (\Exception $e) {
            error_log("Admin product delete error: " . $e->getMessage());
            $this->adminFlash('Ürün silinirken bir hata oluştu.', 'error');
        }

        $this->adminRedirect('/products');
    }

    /**
     * Ürün verilerini doğrula
     */
    private function validateProductData(array $data, int $excludeId = null): array
    {
        $errors = [];

        // Name
        if (empty($data['name'])) {
            $errors[] = 'Ürün adı zorunludur.';
        } elseif (strlen($data['name']) < 3) {
            $errors[] = 'Ürün adı en az 3 karakter olmalıdır.';
        }

        // Price
        if ($data['price'] <= 0) {
            $errors[] = 'Fiyat 0\'dan büyük olmalıdır.';
        }

        // SKU uniqueness
        if (!empty($data['sku'])) {
            if ($this->productRepository->skuExists($data['sku'], $excludeId)) {
                $errors[] = 'Bu SKU zaten kullanılıyor.';
            }
        }

        // Slug uniqueness
        if (!empty($data['slug'])) {
            if ($this->productRepository->slugExists($data['slug'], $excludeId)) {
                $errors[] = 'Bu URL zaten kullanılıyor.';
            }
        }

        return $errors;
    }

    /**
     * Ürün kategorilerini kaydet
     */
    private function saveProductCategories(int $productId, array $categoryIds): void
    {
        $database = $this->app->getDatabase();
        
        // Mevcut kategorileri sil
        $database->delete('product_categories', ['product_id' => $productId]);
        
        // Yeni kategorileri ekle
        foreach ($categoryIds as $categoryId) {
            $database->insert('product_categories', [
                'product_id' => $productId,
                'category_id' => (int) $categoryId
            ]);
        }
    }

    /**
     * Slug oluştur
     */
    private function generateSlug(string $text): string
    {
        // Türkçe karakterleri değiştir
        $text = str_replace(
            ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'],
            ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'],
            $text
        );
        
        // Küçük harfe çevir ve temizle
        $text = strtolower(trim($text));
        $text = preg_replace('/[^a-z0-9-]/', '-', $text);
        $text = preg_replace('/-+/', '-', $text);
        $text = trim($text, '-');
        
        return $text;
    }
}
