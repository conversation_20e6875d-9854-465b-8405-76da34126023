<?php

namespace App\Repositories;

use App\Models\Product;

class ProductRepository extends BaseRepository
{
    protected string $table = 'products';

    public function getFeatured(int $limit = 8): array
    {
        $sql = "
            SELECT p.*, 
                   (SELECT image_url FROM product_images pi WHERE pi.product_id = p.id ORDER BY pi.sort_order LIMIT 1) as main_image
            FROM {$this->table} p 
            WHERE p.status = 'active' AND p.featured = 1 
            ORDER BY p.created_at DESC 
            LIMIT :limit
        ";
        
        return $this->database->fetchAll($sql, ['limit' => $limit]);
    }

    public function getByCategory(int $categoryId, int $limit = 20, int $offset = 0): array
    {
        $sql = "
            SELECT p.*, 
                   (SELECT image_url FROM product_images pi WHERE pi.product_id = p.id ORDER BY pi.sort_order LIMIT 1) as main_image
            FROM {$this->table} p
            INNER JOIN product_categories pc ON p.id = pc.product_id
            WHERE p.status = 'active' AND pc.category_id = :category_id
            ORDER BY p.created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        return $this->database->fetchAll($sql, [
            'category_id' => $categoryId,
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    public function countByCategory(int $categoryId): int
    {
        $sql = "
            SELECT COUNT(*) as count
            FROM {$this->table} p
            INNER JOIN product_categories pc ON p.id = pc.product_id
            WHERE p.status = 'active' AND pc.category_id = :category_id
        ";
        
        $result = $this->database->fetch($sql, ['category_id' => $categoryId]);
        return (int) ($result['count'] ?? 0);
    }

    public function search(string $query, int $limit = 20, int $offset = 0): array
    {
        $sql = "
            SELECT p.*, 
                   (SELECT image_url FROM product_images pi WHERE pi.product_id = p.id ORDER BY pi.sort_order LIMIT 1) as main_image,
                   MATCH(p.name, p.description) AGAINST(:query IN NATURAL LANGUAGE MODE) as relevance
            FROM {$this->table} p
            WHERE p.status = 'active' 
            AND (
                MATCH(p.name, p.description) AGAINST(:query IN NATURAL LANGUAGE MODE)
                OR p.name LIKE :like_query
                OR p.sku LIKE :like_query
            )
            ORDER BY relevance DESC, p.created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $likeQuery = '%' . $query . '%';
        
        return $this->database->fetchAll($sql, [
            'query' => $query,
            'like_query' => $likeQuery,
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    public function countSearch(string $query): int
    {
        $sql = "
            SELECT COUNT(*) as count
            FROM {$this->table} p
            WHERE p.status = 'active' 
            AND (
                MATCH(p.name, p.description) AGAINST(:query IN NATURAL LANGUAGE MODE)
                OR p.name LIKE :like_query
                OR p.sku LIKE :like_query
            )
        ";
        
        $likeQuery = '%' . $query . '%';
        
        $result = $this->database->fetch($sql, [
            'query' => $query,
            'like_query' => $likeQuery
        ]);
        
        return (int) ($result['count'] ?? 0);
    }

    public function getAll(?int $limit = null, int $offset = 0, array $conditions = []): array
    {
        // Backward compatibility: support both 'filters' and direct conditions
        $filters = $conditions['filters'] ?? $conditions;

        $sql = "
            SELECT p.*,
                   (SELECT image_url FROM product_images pi WHERE pi.product_id = p.id ORDER BY pi.sort_order LIMIT 1) as main_image
            FROM {$this->table} p
            WHERE 1=1
        ";

        $params = [];

        // Admin can see all statuses, store only sees active
        if (isset($filters['status'])) {
            $sql .= " AND p.status = :status";
            $params['status'] = $filters['status'];
        } elseif (!isset($filters['admin'])) {
            $sql .= " AND p.status = 'active'";
        }

        // Search filter
        if (!empty($filters['search'])) {
            $sql .= " AND (p.name LIKE :search OR p.sku LIKE :search OR p.description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        // Category filter
        if (!empty($filters['category_id'])) {
            $sql .= " AND EXISTS (SELECT 1 FROM product_categories pc WHERE pc.product_id = p.id AND pc.category_id = :category_id)";
            $params['category_id'] = $filters['category_id'];
        }

        // Featured filter
        if (isset($filters['featured'])) {
            $sql .= " AND p.featured = :featured";
            $params['featured'] = (int) $filters['featured'];
        }

        // Low stock filter
        if (!empty($filters['low_stock'])) {
            $sql .= " AND p.track_inventory = 1 AND p.inventory_quantity <= p.low_stock_threshold";
        }

        // Price filters
        if (!empty($filters['min_price'])) {
            $sql .= " AND p.price >= :min_price";
            $params['min_price'] = $filters['min_price'];
        }

        if (!empty($filters['max_price'])) {
            $sql .= " AND p.price <= :max_price";
            $params['max_price'] = $filters['max_price'];
        }

        if (!empty($filters['in_stock'])) {
            $sql .= " AND (p.track_inventory = 0 OR p.inventory_quantity > 0)";
        }

        // Sorting
        $sortBy = $filters['sort'] ?? 'created_at';
        $sortOrder = $filters['order'] ?? 'DESC';

        $allowedSorts = ['created_at', 'price', 'name', 'inventory_quantity'];
        if (!in_array($sortBy, $allowedSorts)) {
            $sortBy = 'created_at';
        }

        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';

        $sql .= " ORDER BY p.{$sortBy} {$sortOrder}";

        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
            $params['limit'] = $limit;
            $params['offset'] = $offset;
        }

        return $this->database->fetchAll($sql, $params);
    }

    public function countAll(array $filters = []): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} p WHERE p.status = 'active'";
        $params = [];

        // Apply same filters as getAll
        if (!empty($filters['category_id'])) {
            $sql .= " AND EXISTS (SELECT 1 FROM product_categories pc WHERE pc.product_id = p.id AND pc.category_id = :category_id)";
            $params['category_id'] = $filters['category_id'];
        }

        if (!empty($filters['min_price'])) {
            $sql .= " AND p.price >= :min_price";
            $params['min_price'] = $filters['min_price'];
        }

        if (!empty($filters['max_price'])) {
            $sql .= " AND p.price <= :max_price";
            $params['max_price'] = $filters['max_price'];
        }

        if (!empty($filters['in_stock'])) {
            $sql .= " AND (p.track_inventory = 0 OR p.inventory_quantity > 0)";
        }

        $result = $this->database->fetch($sql, $params);
        return (int) ($result['count'] ?? 0);
    }

    public function getImages(int $productId): array
    {
        $sql = "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY sort_order";
        return $this->database->fetchAll($sql, ['product_id' => $productId]);
    }

    public function updateStock(int $productId, int $quantity): bool
    {
        $sql = "UPDATE {$this->table} SET inventory_quantity = :quantity, updated_at = NOW() WHERE id = :id";
        $statement = $this->database->query($sql, [
            'quantity' => $quantity,
            'id' => $productId
        ]);
        
        return $statement->rowCount() > 0;
    }

    public function getRelated(int $productId, int $limit = 4): array
    {
        $sql = "
            SELECT DISTINCT p.*, 
                   (SELECT image_url FROM product_images pi WHERE pi.product_id = p.id ORDER BY pi.sort_order LIMIT 1) as main_image
            FROM {$this->table} p
            INNER JOIN product_categories pc1 ON p.id = pc1.product_id
            INNER JOIN product_categories pc2 ON pc1.category_id = pc2.category_id
            WHERE pc2.product_id = :product_id 
            AND p.id != :product_id 
            AND p.status = 'active'
            ORDER BY RAND()
            LIMIT :limit
        ";
        
        return $this->database->fetchAll($sql, [
            'product_id' => $productId,
            'limit' => $limit
        ]);
    }

    public function findBySlug(string $slug): ?array
    {
        $sql = "
            SELECT p.*, 
                   (SELECT JSON_ARRAYAGG(image_url) FROM product_images pi WHERE pi.product_id = p.id ORDER BY pi.sort_order) as images
            FROM {$this->table} p 
            WHERE p.slug = :slug AND p.status = 'active' 
            LIMIT 1
        ";
        
        return $this->database->fetch($sql, ['slug' => $slug]);
    }

    /**
     * Find product by ID and return Product model
     */
    public function findProductById(int $id): ?Product
    {
        $productData = $this->findById($id);
        return $productData ? new Product($productData) : null;
    }

    /**
     * Find product by slug and return Product model
     */
    public function findProductBySlug(string $slug): ?Product
    {
        $productData = $this->findBySlug($slug);
        return $productData ? new Product($productData) : null;
    }

    /**
     * Create new product
     */
    public function createProduct(array $data): ?Product
    {
        // Set default values
        $data['status'] = $data['status'] ?? 'active';
        $data['featured'] = $data['featured'] ?? 0;
        $data['track_inventory'] = $data['track_inventory'] ?? 1;
        $data['inventory_quantity'] = $data['inventory_quantity'] ?? 0;
        $data['low_stock_threshold'] = $data['low_stock_threshold'] ?? 5;
        $data['requires_shipping'] = $data['requires_shipping'] ?? 1;
        $data['is_digital'] = $data['is_digital'] ?? 0;

        $id = $this->create($data);

        if ($id) {
            return $this->findProductById($id);
        }

        return null;
    }

    /**
     * Get products with Product model
     */
    public function getProducts(int $limit = 20, int $offset = 0): array
    {
        $sql = "
            SELECT p.*,
                   (SELECT image_url FROM product_images pi WHERE pi.product_id = p.id ORDER BY pi.sort_order LIMIT 1) as main_image
            FROM {$this->table} p
            WHERE p.status = 'active'
            ORDER BY p.created_at DESC
            LIMIT {$limit} OFFSET {$offset}
        ";

        $products = $this->database->fetchAll($sql);
        return array_map(fn($productData) => new Product($productData), $products);
    }

    /**
     * Get featured products with Product model
     */
    public function getFeaturedProducts(int $limit = 8): array
    {
        $products = $this->getFeatured($limit);
        return array_map(fn($productData) => new Product($productData), $products);
    }

    /**
     * Search products with Product model
     */
    public function searchProducts(string $query, int $limit = 20, int $offset = 0): array
    {
        $products = $this->search($query, $limit, $offset);
        return array_map(fn($productData) => new Product($productData), $products);
    }
}
