<?php

namespace App\Middleware;

use Core\Application;

class AdminMiddleware
{
    public function handle(): bool
    {
        $app = Application::getInstance();
        $auth = $app->getAuthManager();
        
        // Check if user is authenticated
        if (!$auth->check()) {
            $this->redirectToAdminLogin();
            return false;
        }
        
        // Check if user is admin
        if (!$auth->isAdmin()) {
            $this->accessDenied();
            return false;
        }
        
        return true;
    }
    
    private function redirectToAdminLogin(): void
    {
        $currentUrl = $_SERVER['REQUEST_URI'] ?? '/admin';
        $loginUrl = '/admin/login';
        
        // Add return URL if not already on admin login page
        if ($currentUrl !== '/admin/login') {
            $loginUrl .= '?return=' . urlencode($currentUrl);
        }
        
        header("Location: {$loginUrl}");
        exit;
    }
    
    private function accessDenied(): void
    {
        http_response_code(403);
        echo "<h1>403 - <PERSON><PERSON><PERSON><PERSON></h1>";
        echo "<p>Bu say<PERSON>ya eri<PERSON>im yet<PERSON> bulunmamaktadır.</p>";
        echo "<a href='/'>Ana <PERSON>faya Dön</a>";
        exit;
    }
}
