<?php

namespace App\Middleware;

use Core\Application;
use App\Models\User;

class AdminMiddleware
{
    public function handle(): bool
    {
        $app = Application::getInstance();
        $session = $app->getSessionManager();

        // Check if user is authenticated
        if (!$session->has('user_id')) {
            $this->redirectToAdminLogin();
            return false;
        }

        $userId = $session->get('user_id');

        try {
            // Get user from database
            $user = User::find($userId);

            if (!$user) {
                $session->destroy();
                $this->redirectToAdminLogin();
                return false;
            }

            // Check if user is active
            if (!$user->isActive()) {
                $session->destroy();
                $this->redirectToAdminLogin('Hesabınız deaktif edilmiş.');
                return false;
            }

            // Check if user is admin
            if (!$user->isAdmin()) {
                $this->accessDenied();
                return false;
            }

            // Update last activity
            $user->updateLastActivity();

            return true;

        } catch (\Exception $e) {
            error_log("Admin middleware error: " . $e->getMessage());
            $session->destroy();
            $this->redirectToAdminLogin('Bir hata oluştu. Lütfen tekrar giriş yapın.');
            return false;
        }
    }
    
    private function redirectToAdminLogin(?string $message = null): void
    {
        $app = Application::getInstance();
        $session = $app->getSessionManager();

        if ($message) {
            $session->setFlash('error', $message);
        }

        $currentUrl = $_SERVER['REQUEST_URI'] ?? '';
        if ($currentUrl && $currentUrl !== '/admin/login') {
            $session->set('intended_url', $currentUrl);
        }

        header('Location: /admin/login');
        exit;
    }
    
    private function accessDenied(): void
    {
        http_response_code(403);
        echo "<h1>403 - Erişim Reddedildi</h1>";
        echo "<p>Bu sayfaya erişim yetkiniz bulunmamaktadır.</p>";
        echo "<a href='/'>Ana Sayfaya Dön</a>";
        exit;
    }
}
