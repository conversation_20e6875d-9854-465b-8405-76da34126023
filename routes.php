<?php

/*
|--------------------------------------------------------------------------
| EticSimple Routes
|--------------------------------------------------------------------------
| Monolitik uygulama route tanımlamaları
| Store (Mağaza) ve Admin panel route'ları
|--------------------------------------------------------------------------
*/

$app = \Core\Application::getInstance();
$router = $app->getRouter();

/*
|--------------------------------------------------------------------------
| Store Routes (Mağaza)
|--------------------------------------------------------------------------
*/

// Ana sayfa
$router->get('/', 'App\Controllers\Store\HomeController@index');

// Ürünler
$router->get('/products', 'App\Controllers\Store\ProductController@index');
$router->get('/products/{slug}', 'App\Controllers\Store\ProductController@show');
$router->get('/category/{slug}', 'App\Controllers\Store\ProductController@category');
$router->get('/search', 'App\Controllers\Store\ProductController@search');

// Sepet
$router->get('/cart', 'App\Controllers\Store\CartController@index');
$router->post('/cart/add', 'App\Controllers\Store\CartController@add');
$router->post('/cart/update', 'App\Controllers\Store\CartController@update');
$router->post('/cart/remove', 'App\Controllers\Store\CartController@remove');

// Ödeme
$router->get('/checkout', 'App\Controllers\Store\CheckoutController@index', ['App\Middleware\AuthMiddleware']);
$router->post('/checkout/process', 'App\Controllers\Store\CheckoutController@process', ['App\Middleware\AuthMiddleware']);

// Kullanıcı hesabı
$router->get('/account', 'App\Controllers\Store\UserController@dashboard', ['App\Middleware\AuthMiddleware']);
$router->get('/account/orders', 'App\Controllers\Store\UserController@orders', ['App\Middleware\AuthMiddleware']);
$router->get('/account/addresses', 'App\Controllers\Store\UserController@addresses', ['App\Middleware\AuthMiddleware']);
$router->post('/account/addresses', 'App\Controllers\Store\UserController@storeAddress', ['App\Middleware\AuthMiddleware']);
$router->post('/account/theme', 'App\Controllers\Store\UserController@updateTheme', ['App\Middleware\AuthMiddleware']);

// Authentication
$router->get('/login', 'App\Controllers\Store\AuthController@loginForm');
$router->post('/login', 'App\Controllers\Store\AuthController@login');
$router->get('/register', 'App\Controllers\Store\AuthController@registerForm');
$router->post('/register', 'App\Controllers\Store\AuthController@register');
$router->post('/logout', 'App\Controllers\Store\AuthController@logout');
$router->get('/forgot-password', 'App\Controllers\Store\AuthController@forgotPasswordForm');
$router->post('/forgot-password', 'App\Controllers\Store\AuthController@forgotPassword');
$router->get('/reset-password/{token}', 'App\Controllers\Store\AuthController@resetPasswordForm');
$router->post('/reset-password', 'App\Controllers\Store\AuthController@resetPassword');

/*
|--------------------------------------------------------------------------
| Admin Routes (Admin Panel)
|--------------------------------------------------------------------------
*/

// Admin authentication
$router->get('/admin/login', 'App\Controllers\Admin\AuthController@loginForm');
$router->post('/admin/login', 'App\Controllers\Admin\AuthController@login');
$router->post('/admin/logout', 'App\Controllers\Admin\AuthController@logout');

// Admin dashboard
$router->get('/admin', 'App\Controllers\Admin\DashboardController@index', ['App\Middleware\AdminMiddleware']);

// Admin ürün yönetimi
$router->get('/admin/products', 'App\Controllers\Admin\ProductController@index', ['App\Middleware\AdminMiddleware']);
$router->get('/admin/products/create', 'App\Controllers\Admin\ProductController@create', ['App\Middleware\AdminMiddleware']);
$router->post('/admin/products', 'App\Controllers\Admin\ProductController@store', ['App\Middleware\AdminMiddleware']);
$router->get('/admin/products/{id}/edit', 'App\Controllers\Admin\ProductController@edit', ['App\Middleware\AdminMiddleware']);
$router->put('/admin/products/{id}', 'App\Controllers\Admin\ProductController@update', ['App\Middleware\AdminMiddleware']);
$router->delete('/admin/products/{id}', 'App\Controllers\Admin\ProductController@delete', ['App\Middleware\AdminMiddleware']);

// Admin sipariş yönetimi
$router->get('/admin/orders', 'App\Controllers\Admin\OrderController@index', ['App\Middleware\AdminMiddleware']);
$router->get('/admin/orders/{id}', 'App\Controllers\Admin\OrderController@show', ['App\Middleware\AdminMiddleware']);
$router->put('/admin/orders/{id}/status', 'App\Controllers\Admin\OrderController@updateStatus', ['App\Middleware\AdminMiddleware']);

// Admin kullanıcı yönetimi
$router->get('/admin/users', 'App\Controllers\Admin\UserController@index', ['App\Middleware\AdminMiddleware']);
$router->get('/admin/users/{id}', 'App\Controllers\Admin\UserController@show', ['App\Middleware\AdminMiddleware']);

// Admin tema yönetimi
$router->get('/admin/themes', 'App\Controllers\Admin\ThemeController@index', ['App\Middleware\AdminMiddleware']);
$router->post('/admin/themes/activate', 'App\Controllers\Admin\ThemeController@activate', ['App\Middleware\AdminMiddleware']);

// Admin ayarlar
$router->get('/admin/settings', 'App\Controllers\Admin\SettingsController@index', ['App\Middleware\AdminMiddleware']);
$router->post('/admin/settings', 'App\Controllers\Admin\SettingsController@update', ['App\Middleware\AdminMiddleware']);
