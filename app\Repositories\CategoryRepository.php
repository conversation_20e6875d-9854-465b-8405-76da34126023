<?php

namespace App\Repositories;

class CategoryRepository extends BaseRepository
{
    protected string $table = 'categories';

    public function getMain(int $limit = 6): array
    {
        $sql = "
            SELECT c.*,
                   (SELECT COUNT(*) FROM product_categories pc 
                    INNER JOIN products p ON pc.product_id = p.id 
                    WHERE pc.category_id = c.id AND p.status = 'active') as product_count
            FROM {$this->table} c 
            WHERE c.parent_id IS NULL AND c.is_active = 1 
            ORDER BY c.sort_order ASC, c.name ASC 
            LIMIT :limit
        ";
        
        return $this->database->fetchAll($sql, ['limit' => $limit]);
    }

    public function getWithChildren(): array
    {
        $sql = "
            SELECT c.*,
                   (SELECT COUNT(*) FROM product_categories pc 
                    INNER JOIN products p ON pc.product_id = p.id 
                    WHERE pc.category_id = c.id AND p.status = 'active') as product_count
            FROM {$this->table} c 
            WHERE c.is_active = 1 
            ORDER BY c.parent_id ASC, c.sort_order ASC, c.name ASC
        ";
        
        $categories = $this->database->fetchAll($sql);
        
        // Build tree structure
        return $this->buildCategoryTree($categories);
    }

    public function getChildren(int $parentId): array
    {
        $sql = "
            SELECT c.*,
                   (SELECT COUNT(*) FROM product_categories pc 
                    INNER JOIN products p ON pc.product_id = p.id 
                    WHERE pc.category_id = c.id AND p.status = 'active') as product_count
            FROM {$this->table} c 
            WHERE c.parent_id = :parent_id AND c.is_active = 1 
            ORDER BY c.sort_order ASC, c.name ASC
        ";
        
        return $this->database->fetchAll($sql, ['parent_id' => $parentId]);
    }

    public function getParents(int $categoryId): array
    {
        $parents = [];
        $currentId = $categoryId;
        
        while ($currentId) {
            $category = $this->findById($currentId);
            if (!$category) {
                break;
            }
            
            array_unshift($parents, $category);
            $currentId = $category['parent_id'];
        }
        
        return $parents;
    }

    public function getBreadcrumb(int $categoryId): array
    {
        $breadcrumb = [];
        $parents = $this->getParents($categoryId);
        
        foreach ($parents as $parent) {
            $breadcrumb[] = [
                'id' => $parent['id'],
                'name' => $parent['name'],
                'slug' => $parent['slug'],
                'url' => '/category/' . $parent['slug']
            ];
        }
        
        return $breadcrumb;
    }

    public function findBySlug(string $slug): ?array
    {
        $sql = "
            SELECT c.*,
                   (SELECT COUNT(*) FROM product_categories pc 
                    INNER JOIN products p ON pc.product_id = p.id 
                    WHERE pc.category_id = c.id AND p.status = 'active') as product_count
            FROM {$this->table} c 
            WHERE c.slug = :slug AND c.is_active = 1 
            LIMIT 1
        ";
        
        return $this->database->fetch($sql, ['slug' => $slug]);
    }

    public function getForNavigation(): array
    {
        $sql = "
            SELECT c.id, c.parent_id, c.name, c.slug, c.sort_order
            FROM {$this->table} c 
            WHERE c.is_active = 1 
            ORDER BY c.parent_id ASC, c.sort_order ASC, c.name ASC
        ";
        
        $categories = $this->database->fetchAll($sql);
        
        return $this->buildCategoryTree($categories);
    }

    private function buildCategoryTree(array $categories, int $parentId = null): array
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['children'] = $this->buildCategoryTree($categories, $category['id']);
                $tree[] = $category;
            }
        }
        
        return $tree;
    }

    public function updateSortOrder(int $categoryId, int $sortOrder): bool
    {
        $sql = "UPDATE {$this->table} SET sort_order = :sort_order, updated_at = NOW() WHERE id = :id";
        $statement = $this->database->query($sql, [
            'sort_order' => $sortOrder,
            'id' => $categoryId
        ]);
        
        return $statement->rowCount() > 0;
    }

    public function getMaxSortOrder(int $parentId = null): int
    {
        if ($parentId === null) {
            $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table} WHERE parent_id IS NULL";
            $params = [];
        } else {
            $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table} WHERE parent_id = :parent_id";
            $params = ['parent_id' => $parentId];
        }
        
        $result = $this->database->fetch($sql, $params);
        return (int) ($result['max_order'] ?? 0);
    }

    public function hasProducts(int $categoryId): bool
    {
        $sql = "
            SELECT COUNT(*) as count 
            FROM product_categories pc 
            INNER JOIN products p ON pc.product_id = p.id 
            WHERE pc.category_id = :category_id AND p.status = 'active'
        ";
        
        $result = $this->database->fetch($sql, ['category_id' => $categoryId]);
        return (int) ($result['count'] ?? 0) > 0;
    }

    public function hasChildren(int $categoryId): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE parent_id = :parent_id AND is_active = 1";
        $result = $this->database->fetch($sql, ['parent_id' => $categoryId]);
        return (int) ($result['count'] ?? 0) > 0;
    }

    public function canDelete(int $categoryId): bool
    {
        return !$this->hasProducts($categoryId) && !$this->hasChildren($categoryId);
    }

    public function getPopular(int $limit = 10): array
    {
        $sql = "
            SELECT c.*, COUNT(pc.product_id) as product_count
            FROM {$this->table} c
            INNER JOIN product_categories pc ON c.id = pc.category_id
            INNER JOIN products p ON pc.product_id = p.id
            WHERE c.is_active = 1 AND p.status = 'active'
            GROUP BY c.id
            ORDER BY product_count DESC, c.name ASC
            LIMIT :limit
        ";
        
        return $this->database->fetchAll($sql, ['limit' => $limit]);
    }
}
