/*
|--------------------------------------------------------------------------
| EticSimple Admin CSS
|--------------------------------------------------------------------------
| Admin panel için özel CSS stilleri
| Monolitik yapıda admin/mağaza ayrımı
|--------------------------------------------------------------------------
*/

/* Admin Layout */
.admin-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--theme-bg-secondary);
}

.admin-sidebar {
    width: 260px;
    background-color: var(--theme-bg-primary);
    border-right: 1px solid var(--theme-border-primary);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.admin-main {
    flex: 1;
    margin-left: 260px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.admin-header {
    background-color: var(--theme-bg-primary);
    border-bottom: 1px solid var(--theme-border-primary);
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 999;
}

.admin-content {
    flex: 1;
    padding: 2rem;
}

.admin-footer {
    background-color: var(--theme-bg-primary);
    border-top: 1px solid var(--theme-border-primary);
    padding: 1rem 2rem;
    text-align: center;
    color: var(--theme-text-muted);
    font-size: 0.875rem;
}

/* Sidebar */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--theme-border-primary);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
}

.sidebar-logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--theme-accent-primary), var(--theme-accent-secondary));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
}

.sidebar-logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--theme-text-primary);
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    padding: 0.5rem 1.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--theme-text-muted);
    margin-bottom: 0.5rem;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--theme-text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);
    border-left-color: var(--theme-accent-primary);
}

.nav-link.active {
    background-color: rgba(14, 165, 233, 0.1);
    color: var(--theme-accent-primary);
    border-left-color: var(--theme-accent-primary);
    font-weight: 500;
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-badge {
    margin-left: auto;
    background-color: var(--theme-error);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* Header */
.header-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--theme-text-primary);
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-search {
    position: relative;
}

.header-search-input {
    width: 300px;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid var(--theme-border-primary);
    border-radius: var(--radius-md);
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);
    font-size: 0.875rem;
}

.header-search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--theme-text-muted);
}

.header-notifications {
    position: relative;
}

.notification-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid var(--theme-border-primary);
    background-color: var(--theme-bg-primary);
    color: var(--theme-text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.notification-button:hover {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    background-color: var(--theme-error);
    color: white;
    border-radius: 50%;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.header-user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.header-user:hover {
    background-color: var(--theme-bg-secondary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--theme-accent-primary), var(--theme-accent-secondary));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
    color: var(--theme-text-primary);
    font-size: 0.875rem;
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--theme-text-muted);
    line-height: 1.2;
}

/* Content Helpers */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--theme-border-primary);
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--theme-text-primary);
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

.content-card {
    background-color: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: 2rem;
}

.content-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--theme-border-primary);
    background-color: var(--theme-bg-secondary);
}

.content-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-text-primary);
    margin: 0;
}

.content-card-body {
    padding: 1.5rem;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--theme-bg-primary);
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--theme-border-primary);
}

.admin-table th {
    background-color: var(--theme-bg-secondary);
    font-weight: 600;
    color: var(--theme-text-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-table tbody tr:hover {
    background-color: var(--theme-bg-secondary);
}

/* Forms */
.admin-form {
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--theme-border-primary);
    margin-top: 2rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        width: 280px;
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-header {
        padding: 1rem;
    }
    
    .admin-content {
        padding: 1rem;
    }
    
    .header-search-input {
        width: 200px;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

/* Sidebar Toggle */
.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--theme-text-primary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
}

@media (max-width: 768px) {
    .sidebar-toggle {
        display: block;
    }
}

/* Overlay for mobile sidebar */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

@media (max-width: 768px) {
    .sidebar-overlay.active {
        display: block;
    }
}
