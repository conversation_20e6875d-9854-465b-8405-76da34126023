<?php $this->extends('store.layouts.app'); ?>

<?php $this->section('content'); ?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">EticSimple'a Hoş Geldiniz</h1>
            <p class="hero-subtitle">Türkiye'nin en güvenilir e-ticaret platformu</p>
            <a href="/products" class="btn btn-primary btn-lg">Ürünleri İncele</a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<?php if (!empty($categories)): ?>
<section class="categories-section">
    <div class="container">
        <h2 class="section-title">Kategoriler</h2>
        <div class="categories-grid">
            <?php foreach ($categories as $category): ?>
            <div class="category-card">
                <a href="/category/<?= $this->e($category['slug']) ?>" class="category-link">
                    <?php if (!empty($category['image'])): ?>
                    <img src="<?= $this->e($category['image']) ?>" alt="<?= $this->e($category['name']) ?>" class="category-image">
                    <?php endif; ?>
                    <h3 class="category-name"><?= $this->e($category['name']) ?></h3>
                </a>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Featured Products Section -->
<?php if (!empty($featuredProducts)): ?>
<section class="featured-products-section">
    <div class="container">
        <h2 class="section-title">Öne Çıkan Ürünler</h2>
        <div class="products-grid">
            <?php foreach ($featuredProducts as $product): ?>
            <?php $this->component('product-card', ['product' => $product]); ?>
            <?php endforeach; ?>
        </div>
        <div class="section-footer">
            <a href="/products" class="btn btn-outline">Tüm Ürünleri Gör</a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="icon-truck"></i>
                </div>
                <h3 class="feature-title">Ücretsiz Kargo</h3>
                <p class="feature-description">150₺ ve üzeri alışverişlerde ücretsiz kargo</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="icon-shield"></i>
                </div>
                <h3 class="feature-title">Güvenli Ödeme</h3>
                <p class="feature-description">SSL sertifikası ile güvenli ödeme</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="icon-return"></i>
                </div>
                <h3 class="feature-title">Kolay İade</h3>
                <p class="feature-description">14 gün içinde kolay iade</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="icon-support"></i>
                </div>
                <h3 class="feature-title">7/24 Destek</h3>
                <p class="feature-description">Her zaman yanınızdayız</p>
            </div>
        </div>
    </div>
</section>

<?php $this->endSection(); ?>

<?php $this->section('styles'); ?>
<style>
/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--theme-accent-primary), var(--theme-accent-secondary));
    color: white;
    padding: 80px 0;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Categories */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.category-card {
    background: var(--theme-bg-primary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.category-name {
    padding: 1rem;
    text-align: center;
    color: var(--theme-text-primary);
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Features */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    font-size: 3rem;
    color: var(--theme-accent-primary);
    margin-bottom: 1rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--theme-text-primary);
}

.feature-description {
    color: var(--theme-text-secondary);
}
</style>
<?php $this->endSection(); ?>
