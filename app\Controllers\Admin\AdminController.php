<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use Core\Session\SessionManager;
use App\Repositories\UserRepository;
use App\Repositories\ProductRepository;
use App\Repositories\CategoryRepository;

class AdminController extends BaseController
{
    protected UserRepository $userRepository;
    protected ProductRepository $productRepository;
    protected CategoryRepository $categoryRepository;

    public function __construct()
    {
        parent::__construct();

        $this->userRepository = new UserRepository($this->app->getDatabase());
        $this->productRepository = new ProductRepository($this->app->getDatabase());
        $this->categoryRepository = new CategoryRepository($this->app->getDatabase());
    }

    /**
     * Admin giriş formu
     */
    public function loginForm(): void
    {
        // Zaten giriş yapmışsa dashboard'a yönlendir
        if ($this->isLoggedIn()) {
            $this->adminRedirect('/');
            return;
        }

        $data = [
            'title' => 'Admin Giriş',
            'email' => $this->getInput('email', ''),
            'remember' => $this->getInput('remember', false)
        ];

        $this->view('admin/auth/login', $data);
    }

    /**
     * Admin giriş işlemi
     */
    public function login(): void
    {
        if (!$this->isPost()) {
            $this->adminRedirect('/login');
            return;
        }

        // CSRF kontrolü
        if (!$this->verifyCsrf()) {
            $this->adminFlash('Güvenlik hatası. Lütfen tekrar deneyin.', 'error');
            $this->back();
            return;
        }

        $email = $this->getInput('email');
        $password = $this->getInput('password');
        $remember = (bool) $this->getInput('remember');

        // Validation
        if (empty($email) || empty($password)) {
            $this->adminFlash('E-posta ve şifre zorunludur.', 'error');
            $this->back();
            return;
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->adminFlash('Geçerli bir e-posta adresi girin.', 'error');
            $this->back();
            return;
        }

        try {
            // Kullanıcıyı bul
            $user = $this->userRepository->findByEmail($email);

            if (!$user) {
                $this->adminFlash('E-posta veya şifre hatalı.', 'error');
                $this->back();
                return;
            }

            // Şifre kontrolü
            if (!$user->verifyPassword($password)) {
                $this->adminFlash('E-posta veya şifre hatalı.', 'error');
                $this->back();
                return;
            }

            // Kullanıcı aktif mi?
            if (!$user->isActive()) {
                $this->adminFlash('Hesabınız deaktif edilmiş.', 'error');
                $this->back();
                return;
            }

            // Admin yetkisi var mı?
            if (!$user->isAdmin()) {
                $this->adminFlash('Bu sayfaya erişim yetkiniz yok.', 'error');
                $this->back();
                return;
            }

            // Giriş başarılı
            $session = $this->app->getSessionManager();
            $session->set('user_id', $user->id);
            $session->set('user_email', $user->email);
            $session->set('user_role', $user->role);

            if ($remember) {
                // Remember me cookie (30 gün)
                $token = bin2hex(random_bytes(32));
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                $user->remember_token = $token;
                $user->save();
            }

            // Son giriş zamanını güncelle
            $user->updateLastLogin();

            $this->adminFlash('Hoş geldiniz!', 'success');

            // Intended URL varsa oraya yönlendir
            $intendedUrl = $session->get('intended_url');
            if ($intendedUrl) {
                $session->remove('intended_url');
                $this->redirect($intendedUrl);
            } else {
                $this->adminRedirect('/');
            }

        } catch (\Exception $e) {
            error_log("Admin login error: " . $e->getMessage());
            $this->adminFlash('Giriş sırasında bir hata oluştu.', 'error');
            $this->back();
        }
    }

    /**
     * Admin dashboard
     */
    public function dashboard(): void
    {
        try {
            // Dashboard istatistikleri
            $stats = $this->getDashboardStats();
            
            // Son siparişler
            $recentOrders = $this->getRecentOrders(10);
            
            // Düşük stoklu ürünler
            $lowStockProducts = $this->getLowStockProducts(10);
            
            // Son kayıt olan kullanıcılar
            $recentUsers = $this->userRepository->getActiveUsers(5);

            $data = [
                'title' => 'Admin Dashboard',
                'meta_description' => 'EticSimple Admin Panel',
                'stats' => $stats,
                'recent_orders' => $recentOrders,
                'low_stock_products' => $lowStockProducts,
                'recent_users' => $recentUsers,
                'user' => $this->user(),
            ];

            $this->view('admin/pages/dashboard', $data);

        } catch (\Exception $e) {
            error_log("Admin dashboard error: " . $e->getMessage());
            
            $data = [
                'title' => 'Admin Dashboard',
                'user' => $this->user(),
                'error' => 'Dashboard yüklenirken bir hata oluştu.'
            ];

            $this->view('admin/pages/dashboard', $data);
        }
    }

    /**
     * Admin çıkış işlemi
     */
    public function logout(): void
    {
        if (!$this->isPost()) {
            $this->adminRedirect('/');
            return;
        }

        // CSRF kontrolü
        if (!$this->verifyCsrf()) {
            $this->adminRedirect('/');
            return;
        }

        try {
            // Remember token'ı temizle
            if (isset($_COOKIE['remember_token'])) {
                $user = $this->user();
                if ($user) {
                    $user->remember_token = null;
                    $user->save();
                }
                setcookie('remember_token', '', time() - 3600, '/', '', true, true);
            }

            // Session'ı temizle
            $session = $this->app->getSessionManager();
            $session->destroy();

            $this->adminFlash('Başarıyla çıkış yaptınız.', 'success');
            $this->redirect('/admin/login');

        } catch (\Exception $e) {
            error_log("Admin logout error: " . $e->getMessage());
            $session = $this->app->getSessionManager();
            $session->destroy();
            $this->redirect('/admin/login');
        }
    }

    /**
     * Dashboard istatistiklerini getir
     */
    private function getDashboardStats(): array
    {
        $database = $this->app->getDatabase();
        
        try {
            // Kullanıcı istatistikleri
            $userStats = $this->userRepository->getUserStats();
            
            // Ürün istatistikleri
            $productStats = $this->getProductStats();
            
            // Sipariş istatistikleri
            $orderStats = $this->getOrderStats();
            
            // Kategori sayısı
            $categoryCount = $this->categoryRepository->count();

            return [
                'users' => $userStats,
                'products' => $productStats,
                'orders' => $orderStats,
                'categories' => $categoryCount
            ];

        } catch (\Exception $e) {
            error_log("Dashboard stats error: " . $e->getMessage());
            return [
                'users' => ['total' => 0, 'active' => 0, 'new_30_days' => 0],
                'products' => ['total' => 0, 'active' => 0, 'low_stock' => 0],
                'orders' => ['total' => 0, 'pending' => 0, 'today' => 0],
                'categories' => 0
            ];
        }
    }

    /**
     * Ürün istatistiklerini getir
     */
    private function getProductStats(): array
    {
        $database = $this->app->getDatabase();
        
        $sql = "
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                SUM(CASE WHEN track_inventory = 1 AND inventory_quantity <= low_stock_threshold THEN 1 ELSE 0 END) as low_stock,
                SUM(CASE WHEN featured = 1 THEN 1 ELSE 0 END) as featured
            FROM products
        ";
        
        $result = $database->fetch($sql);
        
        return [
            'total' => (int) ($result['total'] ?? 0),
            'active' => (int) ($result['active'] ?? 0),
            'low_stock' => (int) ($result['low_stock'] ?? 0),
            'featured' => (int) ($result['featured'] ?? 0)
        ];
    }

    /**
     * Sipariş istatistiklerini getir
     */
    private function getOrderStats(): array
    {
        $database = $this->app->getDatabase();
        
        $sql = "
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'shipped' THEN 1 ELSE 0 END) as shipped,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today,
                SUM(total_amount) as total_revenue
            FROM orders
        ";
        
        $result = $database->fetch($sql);
        
        return [
            'total' => (int) ($result['total'] ?? 0),
            'pending' => (int) ($result['pending'] ?? 0),
            'processing' => (int) ($result['processing'] ?? 0),
            'shipped' => (int) ($result['shipped'] ?? 0),
            'delivered' => (int) ($result['delivered'] ?? 0),
            'today' => (int) ($result['today'] ?? 0),
            'total_revenue' => (float) ($result['total_revenue'] ?? 0)
        ];
    }

    /**
     * Son siparişleri getir
     */
    private function getRecentOrders(int $limit = 10): array
    {
        $database = $this->app->getDatabase();
        
        $sql = "
            SELECT o.*, u.first_name, u.last_name, u.email
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            ORDER BY o.created_at DESC
            LIMIT {$limit}
        ";
        
        return $database->fetchAll($sql);
    }

    /**
     * Düşük stoklu ürünleri getir
     */
    private function getLowStockProducts(int $limit = 10): array
    {
        $database = $this->app->getDatabase();
        
        $sql = "
            SELECT p.*, 
                   (SELECT pi.image_url FROM product_images pi 
                    WHERE pi.product_id = p.id AND pi.is_primary = 1 
                    ORDER BY pi.sort_order ASC LIMIT 1) as main_image
            FROM products p
            WHERE p.track_inventory = 1 
            AND p.inventory_quantity <= p.low_stock_threshold
            AND p.status = 'active'
            ORDER BY p.inventory_quantity ASC
            LIMIT {$limit}
        ";
        
        return $database->fetchAll($sql);
    }

    /**
     * Admin yetkisi kontrolü
     */
    protected function isAdmin(): bool
    {
        $user = $this->user();
        return $user && $user->isAdmin();
    }

    /**
     * Admin view helper
     */
    protected function adminView(string $view, array $data = []): void
    {
        // Admin layout'u kullan
        $data['layout'] = 'admin/layouts/main';
        $this->view($view, $data);
    }

    /**
     * JSON response helper
     */
    protected function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Admin flash message helper
     */
    protected function adminFlash(string $message, string $type = 'success'): void
    {
        $this->flash($message, $type);
    }

    /**
     * Admin redirect helper
     */
    protected function adminRedirect(string $path): void
    {
        $this->redirect('/admin' . $path);
    }

    /**
     * Pagination helper
     */
    protected function paginate(int $total, int $page, int $limit): array
    {
        $totalPages = ceil($total / $limit);
        
        return [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_items' => $total,
            'items_per_page' => $limit,
            'has_prev' => $page > 1,
            'has_next' => $page < $totalPages,
            'prev_page' => $page - 1,
            'next_page' => $page + 1,
            'start_item' => (($page - 1) * $limit) + 1,
            'end_item' => min($page * $limit, $total)
        ];
    }

    /**
     * Validate admin permissions
     */
    protected function requirePermission(string $permission): void
    {
        $user = $this->user();

        if (!$user || !$user->hasPermission($permission)) {
            $this->adminFlash('Bu işlem için yetkiniz bulunmuyor.', 'error');
            $this->adminRedirect('/dashboard');
            return;
        }
    }

    /**
     * Admin için auth kontrolü override
     */
    protected function requireAuth(): void
    {
        if (!$this->isLoggedIn()) {
            $this->adminRedirect('/login');
        }
    }

    /**
     * Admin için admin kontrolü override
     */
    protected function requireAdminAuth(): void
    {
        if (!$this->isAdmin()) {
            $this->adminRedirect('/login');
        }
    }
}
