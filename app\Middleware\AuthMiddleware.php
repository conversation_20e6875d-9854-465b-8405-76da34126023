<?php

namespace App\Middleware;

use Core\Application;

class AuthMiddleware
{
    public function handle(): bool
    {
        $app = Application::getInstance();
        $auth = $app->getAuthManager();
        
        // Check if user is authenticated
        if (!$auth->check()) {
            // Try remember token
            if (!$auth->checkRememberToken()) {
                // Redirect to login
                $this->redirectToLogin();
                return false;
            }
        }
        
        return true;
    }
    
    private function redirectToLogin(): void
    {
        $currentUrl = $_SERVER['REQUEST_URI'] ?? '/';
        $loginUrl = '/login';
        
        // Add return URL if not already on login page
        if ($currentUrl !== '/login' && $currentUrl !== '/register') {
            $loginUrl .= '?return=' . urlencode($currentUrl);
        }
        
        header("Location: {$loginUrl}");
        exit;
    }
}
