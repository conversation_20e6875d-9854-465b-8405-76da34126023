<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
| Ana web route'ları
|--------------------------------------------------------------------------
*/

use App\Controllers\HomeController;
use App\Controllers\AuthController;
use App\Controllers\ProductController;
use App\Controllers\CategoryController;
use App\Controllers\CartController;
use App\Controllers\CheckoutController;
use App\Controllers\UserController;

// Ana sayfa
$router->get('/', [HomeController::class, 'index']);

// Ürün sayfaları
$router->get('/products', [ProductController::class, 'index']);
$router->get('/products/{slug}', [ProductController::class, 'show']);

// Kategori sayfaları
$router->get('/categories', [CategoryController::class, 'index']);
$router->get('/categories/{slug}', [CategoryController::class, 'show']);

// Arama
$router->get('/search', [ProductController::class, 'search']);

// Sepet
$router->get('/cart', [CartController::class, 'index']);
$router->post('/cart/add', [CartController::class, 'add']);
$router->post('/cart/update', [CartController::class, 'update']);
$router->post('/cart/remove', [CartController::class, 'remove']);
$router->post('/cart/clear', [CartController::class, 'clear']);

// Ödeme
$router->get('/checkout', [CheckoutController::class, 'index']);
$router->post('/checkout', [CheckoutController::class, 'process']);
$router->get('/checkout/success', [CheckoutController::class, 'success']);
$router->get('/checkout/cancel', [CheckoutController::class, 'cancel']);

// Kullanıcı kimlik doğrulama
$router->get('/login', [AuthController::class, 'loginForm']);
$router->post('/login', [AuthController::class, 'login']);
$router->get('/register', [AuthController::class, 'registerForm']);
$router->post('/register', [AuthController::class, 'register']);
$router->post('/logout', [AuthController::class, 'logout']);

// Şifre sıfırlama
$router->get('/forgot-password', [AuthController::class, 'forgotPasswordForm']);
$router->post('/forgot-password', [AuthController::class, 'forgotPassword']);
$router->get('/reset-password/{token}', [AuthController::class, 'resetPasswordForm']);
$router->post('/reset-password', [AuthController::class, 'resetPassword']);

// E-posta doğrulama
$router->get('/verify-email/{token}', [AuthController::class, 'verifyEmail']);
$router->post('/resend-verification', [AuthController::class, 'resendVerification']);

// Kullanıcı hesabı (giriş yapmış kullanıcılar için)
$router->group(['middleware' => ['auth']], function($router) {
    // Hesap sayfaları
    $router->get('/account', [UserController::class, 'dashboard']);
    $router->get('/account/profile', [UserController::class, 'profile']);
    $router->post('/account/profile', [UserController::class, 'updateProfile']);
    $router->get('/account/password', [UserController::class, 'changePasswordForm']);
    $router->post('/account/password', [UserController::class, 'changePassword']);
    
    // Siparişler
    $router->get('/account/orders', [UserController::class, 'orders']);
    $router->get('/account/orders/{id}', [UserController::class, 'orderDetail']);
    
    // Adresler
    $router->get('/account/addresses', [UserController::class, 'addresses']);
    $router->get('/account/addresses/create', [UserController::class, 'createAddress']);
    $router->post('/account/addresses/create', [UserController::class, 'storeAddress']);
    $router->get('/account/addresses/{id}/edit', [UserController::class, 'editAddress']);
    $router->post('/account/addresses/{id}/edit', [UserController::class, 'updateAddress']);
    $router->post('/account/addresses/{id}/delete', [UserController::class, 'deleteAddress']);
    
    // Favoriler
    $router->get('/account/favorites', [UserController::class, 'favorites']);
    $router->post('/favorites/add', [UserController::class, 'addToFavorites']);
    $router->post('/favorites/remove', [UserController::class, 'removeFromFavorites']);
});

// Statik sayfalar
$router->get('/about', [HomeController::class, 'about']);
$router->get('/contact', [HomeController::class, 'contact']);
$router->post('/contact', [HomeController::class, 'contactSubmit']);
$router->get('/privacy', [HomeController::class, 'privacy']);
$router->get('/terms', [HomeController::class, 'terms']);

// API endpoints (AJAX istekleri için)
$router->group(['prefix' => 'api'], function($router) {
    // Ürün API'leri
    $router->get('/products/search', [ProductController::class, 'apiSearch']);
    $router->get('/products/{id}', [ProductController::class, 'apiShow']);
    
    // Kategori API'leri
    $router->get('/categories', [CategoryController::class, 'apiIndex']);
    $router->get('/categories/{id}/products', [CategoryController::class, 'apiProducts']);
    
    // Sepet API'leri
    $router->get('/cart', [CartController::class, 'apiIndex']);
    $router->post('/cart/add', [CartController::class, 'apiAdd']);
    $router->post('/cart/update', [CartController::class, 'apiUpdate']);
    $router->post('/cart/remove', [CartController::class, 'apiRemove']);
    $router->get('/cart/count', [CartController::class, 'apiCount']);
    
    // Lokasyon API'leri
    $router->get('/cities', [HomeController::class, 'apiCities']);
    $router->get('/cities/{id}/districts', [HomeController::class, 'apiDistricts']);
});

// Admin routes'ları dahil et
require_once __DIR__ . '/admin.php';

// Store routes'ları dahil et  
require_once __DIR__ . '/store.php';
