/*
|--------------------------------------------------------------------------
| EticSimple Admin JavaScript
|--------------------------------------------------------------------------
| Admin panel için özel JavaScript işlevleri
| Sidebar, navigation ve admin-specific özellikler
|--------------------------------------------------------------------------
*/

// Admin Manager Class
class AdminManager {
    constructor() {
        this.initializeSidebar();
        this.initializeNotifications();
        this.initializeDataTables();
        this.initializeConfirmDialogs();
        this.initializeFormValidation();
    }

    initializeSidebar() {
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.admin-sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('open');
                if (overlay) {
                    overlay.classList.toggle('active');
                }
            });
        }

        if (overlay) {
            overlay.addEventListener('click', () => {
                sidebar.classList.remove('open');
                overlay.classList.remove('active');
            });
        }

        // Active navigation highlighting
        this.highlightActiveNavigation();
    }

    highlightActiveNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.startsWith(href) && href !== '/admin') {
                link.classList.add('active');
            } else if (href === '/admin' && currentPath === '/admin') {
                link.classList.add('active');
            }
        });
    }

    initializeNotifications() {
        // Real-time notifications (WebSocket veya polling ile genişletilebilir)
        this.checkNotifications();
        
        // Her 30 saniyede bir kontrol et
        setInterval(() => {
            this.checkNotifications();
        }, 30000);
    }

    async checkNotifications() {
        try {
            const response = await fetch('/admin/notifications/count', {
                headers: {
                    'X-CSRF-TOKEN': window.admin?.csrfToken || ''
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.updateNotificationBadge(data.count || 0);
            }
        } catch (error) {
            console.error('Notification check failed:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    initializeDataTables() {
        // Basit tablo sıralama ve filtreleme
        const tables = document.querySelectorAll('.admin-table');
        
        tables.forEach(table => {
            this.makeTableSortable(table);
        });
    }

    makeTableSortable(table) {
        const headers = table.querySelectorAll('th[data-sortable]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(table, header);
            });
        });
    }

    sortTable(table, header) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = !header.classList.contains('sort-asc');

        // Remove existing sort classes
        header.parentNode.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });

        // Add new sort class
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

        // Sort rows
        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            // Try to parse as numbers
            const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
            const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAscending ? aNum - bNum : bNum - aNum;
            }
            
            // String comparison
            return isAscending 
                ? aValue.localeCompare(bValue, 'tr')
                : bValue.localeCompare(aValue, 'tr');
        });

        // Reorder rows
        rows.forEach(row => tbody.appendChild(row));
    }

    initializeConfirmDialogs() {
        // Silme işlemleri için onay diyalogu
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-confirm]')) {
                e.preventDefault();
                
                const message = e.target.getAttribute('data-confirm') || 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?';
                
                if (confirm(message)) {
                    // Form submit veya link takip et
                    if (e.target.tagName === 'BUTTON' && e.target.form) {
                        e.target.form.submit();
                    } else if (e.target.tagName === 'A') {
                        window.location.href = e.target.href;
                    }
                }
            }
        });
    }

    initializeFormValidation() {
        const forms = document.querySelectorAll('.admin-form');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        // Clear previous errors
        form.querySelectorAll('.field-error').forEach(error => {
            error.remove();
        });
        
        form.querySelectorAll('.form-input.error').forEach(input => {
            input.classList.remove('error');
        });

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'Bu alan zorunludur.');
                isValid = false;
            }
        });

        // Email validation
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                this.showFieldError(field, 'Geçerli bir e-posta adresi girin.');
                isValid = false;
            }
        });

        // T.C. Kimlik No validation
        const tcFields = form.querySelectorAll('[data-tc-kimlik]');
        tcFields.forEach(field => {
            if (field.value && !window.EticSimple.FormValidator.validateTCKimlik(field.value)) {
                this.showFieldError(field, 'Geçerli bir T.C. Kimlik No girin.');
                isValid = false;
            }
        });

        return isValid;
    }

    showFieldError(field, message) {
        field.classList.add('error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        errorElement.style.cssText = `
            color: var(--theme-error);
            font-size: 0.875rem;
            margin-top: 0.25rem;
        `;
        
        field.parentNode.appendChild(errorElement);
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Utility methods
    showToast(message, type = 'info') {
        // Toast notification sistemi
        const toast = document.createElement('div');
        toast.className = `admin-toast toast-${type}`;
        toast.textContent = message;
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideInRight 0.3s ease;
        `;
        
        // Type-specific styling
        switch (type) {
            case 'success':
                toast.style.backgroundColor = 'var(--theme-success)';
                break;
            case 'error':
                toast.style.backgroundColor = 'var(--theme-error)';
                break;
            case 'warning':
                toast.style.backgroundColor = 'var(--theme-warning)';
                break;
            default:
                toast.style.backgroundColor = 'var(--theme-info)';
        }
        
        document.body.appendChild(toast);
        
        // Auto remove
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    async makeRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.admin?.csrfToken || ''
            }
        };

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, mergedOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Request failed:', error);
            this.showToast('İşlem sırasında bir hata oluştu.', 'error');
            throw error;
        }
    }
}

// Bulk Actions Handler
class BulkActionsHandler {
    constructor() {
        this.initializeBulkActions();
    }

    initializeBulkActions() {
        // Select all checkbox
        const selectAllCheckbox = document.querySelector('[data-select-all]');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('[data-bulk-item]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
                this.updateBulkActions();
            });
        }

        // Individual checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-bulk-item]')) {
                this.updateBulkActions();
            }
        });

        // Bulk action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-bulk-action]')) {
                e.preventDefault();
                this.handleBulkAction(e.target);
            }
        });
    }

    updateBulkActions() {
        const selectedItems = document.querySelectorAll('[data-bulk-item]:checked');
        const bulkActions = document.querySelector('.bulk-actions');
        
        if (bulkActions) {
            if (selectedItems.length > 0) {
                bulkActions.style.display = 'flex';
                bulkActions.querySelector('.selected-count').textContent = selectedItems.length;
            } else {
                bulkActions.style.display = 'none';
            }
        }
    }

    handleBulkAction(button) {
        const action = button.getAttribute('data-bulk-action');
        const selectedItems = Array.from(document.querySelectorAll('[data-bulk-item]:checked'))
            .map(checkbox => checkbox.value);

        if (selectedItems.length === 0) {
            alert('Lütfen en az bir öğe seçin.');
            return;
        }

        const confirmMessage = button.getAttribute('data-confirm') || 
            `Seçilen ${selectedItems.length} öğe için bu işlemi gerçekleştirmek istediğinizden emin misiniz?`;

        if (confirm(confirmMessage)) {
            this.executeBulkAction(action, selectedItems);
        }
    }

    async executeBulkAction(action, items) {
        try {
            const response = await window.adminManager.makeRequest('/admin/bulk-actions', {
                method: 'POST',
                body: JSON.stringify({
                    action: action,
                    items: items
                })
            });

            if (response.success) {
                window.adminManager.showToast(response.message || 'İşlem başarıyla tamamlandı.', 'success');
                // Sayfayı yenile veya öğeleri kaldır
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                window.adminManager.showToast(response.message || 'İşlem başarısız.', 'error');
            }
        } catch (error) {
            console.error('Bulk action failed:', error);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize admin manager
    window.adminManager = new AdminManager();
    window.bulkActionsHandler = new BulkActionsHandler();
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        .form-input.error {
            border-color: var(--theme-error) !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        }
        
        .sort-asc::after {
            content: ' ↑';
            color: var(--theme-accent-primary);
        }
        
        .sort-desc::after {
            content: ' ↓';
            color: var(--theme-accent-primary);
        }
    `;
    document.head.appendChild(style);
});

// Global admin utilities
window.AdminUtils = {
    formatPrice: (price) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(price);
    },
    
    formatDate: (date) => {
        return new Intl.DateTimeFormat('tr-TR').format(new Date(date));
    },
    
    formatDateTime: (date) => {
        return new Intl.DateTimeFormat('tr-TR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }
};
