<!DOCTYPE html>
<html lang="tr" data-theme="<?= $currentTheme['slug'] ?? 'light' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= $this->csrf() ?>">
    
    <title><?= isset($title) ? $this->e($title) . ' - ' : '' ?>EticSimple</title>
    
    <!-- SEO Meta Tags -->
    <?php if (isset($metaDescription)): ?>
    <meta name="description" content="<?= $this->e($metaDescription) ?>">
    <?php endif; ?>
    
    <?php if (isset($metaKeywords)): ?>
    <meta name="keywords" content="<?= $this->e($metaKeywords) ?>">
    <?php endif; ?>
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?= isset($title) ? $this->e($title) . ' - ' : '' ?>EticSimple">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= $this->url($_SERVER['REQUEST_URI'] ?? '') ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= $this->asset('images/favicon.ico') ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?= $this->asset('css/app.css') ?>">
    
    <!-- Theme CSS Variables -->
    <style>
        :root {
            <?php if (isset($currentTheme['css_variables'])): ?>
                <?php foreach ($currentTheme['css_variables'] as $key => $value): ?>
                    --<?= $key ?>: <?= $value ?>;
                <?php endforeach; ?>
            <?php endif; ?>
        }
    </style>
    
    <!-- Additional Styles -->
    <?php $this->yield('styles'); ?>
</head>
<body>
    <!-- Header -->
    <?php $this->include('store.components.header', [
        'user' => $user ?? null,
        'cartCount' => $cartCount ?? 0
    ]); ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Flash Messages -->
        <?php $this->include('store.components.flash-messages'); ?>
        
        <!-- Page Content -->
        <?php $this->yield('content'); ?>
    </main>
    
    <!-- Footer -->
    <?php $this->include('store.components.footer'); ?>
    
    <!-- JavaScript -->
    <script src="<?= $this->asset('js/app.js') ?>"></script>
    
    <!-- Theme Controller -->
    <script>
        window.themeController = new ThemeController();
        <?php if (isset($user) && $user): ?>
        window.user = {
            isLoggedIn: true,
            id: <?= $user['id'] ?? 0 ?>,
            name: '<?= $this->e($user['first_name'] ?? '') ?>'
        };
        <?php else: ?>
        window.user = { isLoggedIn: false };
        <?php endif; ?>
    </script>
    
    <!-- Additional Scripts -->
    <?php $this->yield('scripts'); ?>
</body>
</html>
