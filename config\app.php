<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Uygulama Adı
    |--------------------------------------------------------------------------
    */
    'name' => $_ENV['APP_NAME'] ?? 'EticSimple',

    /*
    |--------------------------------------------------------------------------
    | Uygulama Ortamı
    |--------------------------------------------------------------------------
    */
    'env' => $_ENV['APP_ENV'] ?? 'production',

    /*
    |--------------------------------------------------------------------------
    | Debug Modu
    |--------------------------------------------------------------------------
    */
    'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),

    /*
    |--------------------------------------------------------------------------
    | Uygulama URL'si
    |--------------------------------------------------------------------------
    */
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',

    /*
    |--------------------------------------------------------------------------
    | Zaman Dilimi
    |--------------------------------------------------------------------------
    */
    'timezone' => $_ENV['APP_TIMEZONE'] ?? 'Europe/Istanbul',

    /*
    |--------------------------------------------------------------------------
    | Dil Ayarları
    |--------------------------------------------------------------------------
    */
    'locale' => $_ENV['APP_LOCALE'] ?? 'tr',
    'fallback_locale' => 'en',
    'supported_locales' => ['tr', 'en'],

    /*
    |--------------------------------------------------------------------------
    | Uygulama Anahtarı
    |--------------------------------------------------------------------------
    */
    'key' => $_ENV['APP_KEY'] ?? '',

    /*
    |--------------------------------------------------------------------------
    | Şifreleme Algoritması
    |--------------------------------------------------------------------------
    */
    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | Güvenlik Ayarları
    |--------------------------------------------------------------------------
    */
    'security' => [
        'session_lifetime' => (int) ($_ENV['SESSION_LIFETIME'] ?? 120),
        'csrf_token_lifetime' => (int) ($_ENV['CSRF_TOKEN_LIFETIME'] ?? 3600),
        'password_min_length' => (int) ($_ENV['PASSWORD_MIN_LENGTH'] ?? 8),
        'login_attempts_limit' => (int) ($_ENV['LOGIN_ATTEMPTS_LIMIT'] ?? 5),
        'login_lockout_duration' => (int) ($_ENV['LOGIN_LOCKOUT_DURATION'] ?? 900),
    ],

    /*
    |--------------------------------------------------------------------------
    | Dosya Yükleme Ayarları
    |--------------------------------------------------------------------------
    */
    'upload' => [
        'max_size' => (int) ($_ENV['UPLOAD_MAX_SIZE'] ?? 10485760), // 10MB
        'allowed_image_types' => explode(',', $_ENV['ALLOWED_IMAGE_TYPES'] ?? 'jpg,jpeg,png,gif,webp'),
        'allowed_file_types' => explode(',', $_ENV['ALLOWED_FILE_TYPES'] ?? 'pdf,doc,docx'),
        'path' => 'uploads/',
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Ayarları
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'driver' => $_ENV['CACHE_DRIVER'] ?? 'redis',
        'prefix' => $_ENV['CACHE_PREFIX'] ?? 'eticsimple_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Log Ayarları
    |--------------------------------------------------------------------------
    */
    'log' => [
        'channel' => $_ENV['LOG_CHANNEL'] ?? 'daily',
        'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
        'days' => (int) ($_ENV['LOG_DAYS'] ?? 14),
    ],

    /*
    |--------------------------------------------------------------------------
    | Tema Ayarları
    |--------------------------------------------------------------------------
    */
    'theme' => [
        'default' => $_ENV['DEFAULT_THEME'] ?? 'light',
        'cache_enabled' => filter_var($_ENV['THEME_CACHE_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
    ],
];
