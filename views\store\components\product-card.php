<?php
// Product card component
$product = $product ?? [];
if (empty($product)) return;

$productUrl = '/products/' . ($product['slug'] ?? '');
$mainImage = $product['main_image'] ?? $this->asset('images/placeholder-product.jpg');
$price = $product['price'] ?? 0;
$comparePrice = $product['compare_price'] ?? null;
$hasDiscount = $comparePrice && $comparePrice > $price;
$discountPercentage = $hasDiscount ? round((($comparePrice - $price) / $comparePrice) * 100) : 0;
$isInStock = !$product['track_inventory'] || ($product['inventory_quantity'] ?? 0) > 0;
?>

<div class="product-card" data-product-id="<?= $product['id'] ?? 0 ?>">
    <!-- Product Image -->
    <div class="product-image-container">
        <a href="<?= $productUrl ?>" class="product-image-link">
            <img src="<?= $this->e($mainImage) ?>" 
                 alt="<?= $this->e($product['name'] ?? '') ?>" 
                 class="product-image"
                 loading="lazy">
        </a>
        
        <!-- Badges -->
        <div class="product-badges">
            <?php if ($hasDiscount): ?>
            <span class="badge badge-discount">-%<?= $discountPercentage ?></span>
            <?php endif; ?>
            
            <?php if ($product['featured'] ?? false): ?>
            <span class="badge badge-featured">Öne Çıkan</span>
            <?php endif; ?>
            
            <?php if (!$isInStock): ?>
            <span class="badge badge-out-of-stock">Tükendi</span>
            <?php endif; ?>
        </div>
        
        <!-- Quick Actions -->
        <div class="product-quick-actions">
            <button class="quick-action-btn" title="Hızlı Görüntüle" data-quick-view="<?= $product['id'] ?? 0 ?>">
                <i class="icon-eye"></i>
            </button>
            <button class="quick-action-btn" title="Favorilere Ekle" data-add-to-wishlist="<?= $product['id'] ?? 0 ?>">
                <i class="icon-heart"></i>
            </button>
        </div>
    </div>
    
    <!-- Product Info -->
    <div class="product-info">
        <!-- Product Name -->
        <h3 class="product-name">
            <a href="<?= $productUrl ?>" class="product-name-link">
                <?= $this->e($product['name'] ?? '') ?>
            </a>
        </h3>
        
        <!-- Product Description -->
        <?php if (!empty($product['short_description'])): ?>
        <p class="product-description">
            <?= $this->truncate($this->e($product['short_description']), 80) ?>
        </p>
        <?php endif; ?>
        
        <!-- Product Price -->
        <div class="product-price">
            <span class="current-price"><?= $this->formatPrice($price) ?></span>
            
            <?php if ($hasDiscount): ?>
            <span class="original-price"><?= $this->formatPrice($comparePrice) ?></span>
            <?php endif; ?>
        </div>
        
        <!-- Product Rating (placeholder) -->
        <div class="product-rating">
            <div class="stars">
                <span class="star filled">★</span>
                <span class="star filled">★</span>
                <span class="star filled">★</span>
                <span class="star filled">★</span>
                <span class="star">★</span>
            </div>
            <span class="rating-count">(24 değerlendirme)</span>
        </div>
        
        <!-- Stock Status -->
        <div class="stock-status">
            <?php if ($isInStock): ?>
                <?php if ($product['track_inventory'] && ($product['inventory_quantity'] ?? 0) <= 5): ?>
                <span class="stock-warning">Son <?= $product['inventory_quantity'] ?> adet!</span>
                <?php else: ?>
                <span class="stock-available">Stokta var</span>
                <?php endif; ?>
            <?php else: ?>
            <span class="stock-unavailable">Stokta yok</span>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Product Actions -->
    <div class="product-actions">
        <?php if ($isInStock): ?>
        <button class="btn btn-primary add-to-cart-btn" 
                data-add-to-cart 
                data-product-id="<?= $product['id'] ?? 0 ?>"
                data-quantity="1">
            <i class="icon-cart"></i>
            Sepete Ekle
        </button>
        <?php else: ?>
        <button class="btn btn-secondary" disabled>
            <i class="icon-bell"></i>
            Stokta Gelince Haber Ver
        </button>
        <?php endif; ?>
        
        <a href="<?= $productUrl ?>" class="btn btn-outline">
            Detayları Gör
        </a>
    </div>
</div>

<style>
/* Product Card Styles */
.product-card {
    background-color: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.product-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
    border-color: var(--theme-accent-primary);
}

/* Product Image */
.product-image-container {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background-color: var(--theme-bg-secondary);
}

.product-image-link {
    display: block;
    width: 100%;
    height: 100%;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

/* Badges */
.product-badges {
    position: absolute;
    top: var(--space-2);
    left: var(--space-2);
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.badge {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-discount {
    background-color: var(--theme-error);
    color: white;
}

.badge-featured {
    background-color: var(--theme-warning);
    color: white;
}

.badge-out-of-stock {
    background-color: var(--theme-text-muted);
    color: white;
}

/* Quick Actions */
.product-quick-actions {
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-quick-actions {
    opacity: 1;
}

.quick-action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background-color: var(--theme-bg-primary);
    color: var(--theme-text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.quick-action-btn:hover {
    background-color: var(--theme-accent-primary);
    color: white;
    transform: scale(1.1);
}

/* Product Info */
.product-info {
    padding: var(--space-4);
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.product-name {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.3;
}

.product-name-link {
    color: var(--theme-text-primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

.product-name-link:hover {
    color: var(--theme-accent-primary);
}

.product-description {
    color: var(--theme-text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
    margin: 0;
}

/* Product Price */
.product-price {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin: var(--space-1) 0;
}

.current-price {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--theme-accent-primary);
}

.original-price {
    font-size: 0.875rem;
    color: var(--theme-text-muted);
    text-decoration: line-through;
}

/* Product Rating */
.product-rating {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
}

.stars {
    display: flex;
    gap: 1px;
}

.star {
    color: var(--theme-border-secondary);
    font-size: 1rem;
}

.star.filled {
    color: var(--theme-warning);
}

.rating-count {
    color: var(--theme-text-muted);
}

/* Stock Status */
.stock-status {
    font-size: 0.875rem;
    font-weight: 500;
}

.stock-available {
    color: var(--theme-success);
}

.stock-warning {
    color: var(--theme-warning);
}

.stock-unavailable {
    color: var(--theme-error);
}

/* Product Actions */
.product-actions {
    padding: var(--space-4);
    padding-top: 0;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.add-to-cart-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

/* Responsive */
@media (max-width: 768px) {
    .product-info {
        padding: var(--space-3);
    }
    
    .product-actions {
        padding: var(--space-3);
        padding-top: 0;
    }
    
    .product-quick-actions {
        opacity: 1; /* Always visible on mobile */
    }
}
</style>
