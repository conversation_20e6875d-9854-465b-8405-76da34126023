<?php

namespace App\Services;

use App\Repositories\ProductRepository;
use App\Repositories\CategoryRepository;
use Core\Application;

class ProductService
{
    private ProductRepository $productRepository;
    private CategoryRepository $categoryRepository;

    public function __construct()
    {
        $app = Application::getInstance();
        $this->productRepository = new ProductRepository($app->getDatabase());
        $this->categoryRepository = new CategoryRepository($app->getDatabase());
    }

    public function getFeaturedProducts(int $limit = 8): array
    {
        return $this->productRepository->getFeatured($limit);
    }

    public function getMainCategories(int $limit = 6): array
    {
        return $this->categoryRepository->getMain($limit);
    }

    public function getProductBySlug(string $slug): ?array
    {
        return $this->productRepository->findBySlug($slug);
    }

    public function getProductsByCategory(string $categorySlug, int $page = 1, int $perPage = 20): array
    {
        $category = $this->categoryRepository->findBySlug($categorySlug);
        if (!$category) {
            return ['products' => [], 'total' => 0, 'category' => null];
        }

        $offset = ($page - 1) * $perPage;
        $products = $this->productRepository->getByCategory($category['id'], $limit = $perPage, $offset);
        $total = $this->productRepository->countByCategory($category['id']);

        return [
            'products' => $products,
            'total' => $total,
            'category' => $category,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_pages' => ceil($total / $perPage),
                'total_items' => $total
            ]
        ];
    }

    public function searchProducts(string $query, int $page = 1, int $perPage = 20): array
    {
        $offset = ($page - 1) * $perPage;
        $products = $this->productRepository->search($query, $perPage, $offset);
        $total = $this->productRepository->countSearch($query);

        return [
            'products' => $products,
            'total' => $total,
            'query' => $query,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_pages' => ceil($total / $perPage),
                'total_items' => $total
            ]
        ];
    }

    public function getAllProducts(int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        $products = $this->productRepository->getAll($perPage, $offset, $filters);
        $total = $this->productRepository->countAll($filters);

        return [
            'products' => $products,
            'total' => $total,
            'filters' => $filters,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_pages' => ceil($total / $perPage),
                'total_items' => $total
            ]
        ];
    }

    public function getProductImages(int $productId): array
    {
        return $this->productRepository->getImages($productId);
    }

    public function isInStock(int $productId, int $quantity = 1): bool
    {
        $product = $this->productRepository->findById($productId);
        
        if (!$product || $product['status'] !== 'active') {
            return false;
        }

        if (!$product['track_inventory']) {
            return true;
        }

        return $product['inventory_quantity'] >= $quantity;
    }

    public function updateStock(int $productId, int $quantity): bool
    {
        return $this->productRepository->updateStock($productId, $quantity);
    }

    public function decreaseStock(int $productId, int $quantity): bool
    {
        $product = $this->productRepository->findById($productId);
        
        if (!$product || !$this->isInStock($productId, $quantity)) {
            return false;
        }

        if (!$product['track_inventory']) {
            return true;
        }

        $newQuantity = $product['inventory_quantity'] - $quantity;
        return $this->updateStock($productId, $newQuantity);
    }

    public function increaseStock(int $productId, int $quantity): bool
    {
        $product = $this->productRepository->findById($productId);
        
        if (!$product) {
            return false;
        }

        if (!$product['track_inventory']) {
            return true;
        }

        $newQuantity = $product['inventory_quantity'] + $quantity;
        return $this->updateStock($productId, $newQuantity);
    }

    public function getRelatedProducts(int $productId, int $limit = 4): array
    {
        return $this->productRepository->getRelated($productId, $limit);
    }

    public function formatPrice(float $price): string
    {
        return number_format($price, 2, ',', '.') . ' ₺';
    }

    public function calculateDiscountPercentage(float $originalPrice, float $salePrice): int
    {
        if ($originalPrice <= 0 || $salePrice >= $originalPrice) {
            return 0;
        }

        return (int) round((($originalPrice - $salePrice) / $originalPrice) * 100);
    }

    public function getProductUrl(array $product): string
    {
        return '/products/' . $product['slug'];
    }

    public function getCategoryUrl(array $category): string
    {
        return '/category/' . $category['slug'];
    }
}
