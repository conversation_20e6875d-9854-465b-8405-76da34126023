<?php

namespace Core\Template;

class TemplateEngine
{
    private string $viewsPath;
    private array $data = [];
    private array $sections = [];
    private string $currentSection = '';
    private ?string $layout = null;

    public function __construct()
    {
        $this->viewsPath = VIEWS_PATH;
    }

    public function render(string $template, array $data = []): void
    {
        $this->data = array_merge($this->data, $data);
        
        // Start output buffering
        ob_start();
        
        try {
            $this->renderTemplate($template);
            $content = ob_get_clean();
            
            // If layout is set, render with layout
            if ($this->layout) {
                $this->data['content'] = $content;
                $this->renderTemplate($this->layout);
                $content = ob_get_clean();
            }
            
            echo $content;
            
        } catch (\Exception $e) {
            ob_end_clean();
            throw $e;
        }
    }

    private function renderTemplate(string $template): void
    {
        $templatePath = $this->getTemplatePath($template);
        
        if (!file_exists($templatePath)) {
            throw new \Exception("Template not found: {$template}");
        }

        // Extract data to variables
        extract($this->data, EXTR_SKIP);
        
        // Include template
        include $templatePath;
    }

    private function getTemplatePath(string $template): string
    {
        // Convert dot notation to path
        $path = str_replace('.', '/', $template);
        
        // Add .php extension if not present
        if (!str_ends_with($path, '.php')) {
            $path .= '.php';
        }
        
        return $this->viewsPath . '/' . $path;
    }

    // Template helper methods (Blade-like syntax)
    
    public function extends(string $layout): void
    {
        $this->layout = $layout;
    }

    public function section(string $name): void
    {
        $this->currentSection = $name;
        ob_start();
    }

    public function endSection(): void
    {
        if ($this->currentSection) {
            $this->sections[$this->currentSection] = ob_get_clean();
            $this->currentSection = '';
        }
    }

    public function yield(string $section, string $default = ''): void
    {
        echo $this->sections[$section] ?? $default;
    }

    public function include(string $template, array $data = []): void
    {
        $originalData = $this->data;
        $this->data = array_merge($this->data, $data);
        
        $this->renderTemplate($template);
        
        $this->data = $originalData;
    }

    public function component(string $component, array $data = []): void
    {
        $this->include("components.{$component}", $data);
    }

    // Utility methods
    
    public function e(string $value): string
    {
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }

    public function asset(string $path): string
    {
        $baseUrl = $this->getBaseUrl();
        return $baseUrl . '/assets/' . ltrim($path, '/');
    }

    public function url(string $path = ''): string
    {
        $baseUrl = $this->getBaseUrl();
        return $baseUrl . '/' . ltrim($path, '/');
    }

    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return "{$protocol}://{$host}";
    }

    public function csrf(): string
    {
        $app = \Core\Application::getInstance();
        $session = $app->getSessionManager();
        return $session->getCsrfToken();
    }

    public function old(string $key, string $default = ''): string
    {
        return $_SESSION['_old'][$key] ?? $default;
    }

    public function formatPrice(float $price, string $currency = '₺'): string
    {
        return number_format($price, 2, ',', '.') . ' ' . $currency;
    }

    public function formatDate(string $date, string $format = 'd.m.Y'): string
    {
        return date($format, strtotime($date));
    }

    public function truncate(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length) . $suffix;
    }
}
